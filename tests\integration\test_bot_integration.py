#!/usr/bin/env python3
"""
Integration tests for Harmony Bot components.
"""

import asyncio
import pytest
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

# Add src directory to Python path
src_path = Path(__file__).parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from harmony.core.bot import HarmonyBot
from harmony.core.events import Event, EventType, EventBus
from harmony.config.settings import HarmonyConfig
from harmony.utils.logger import get_logger

logger = get_logger(__name__)


class TestBotIntegration:
    """Integration tests for the main bot functionality."""
    
    @pytest.fixture
    async def mock_config(self):
        """Create a mock configuration for testing."""
        config = HarmonyConfig.from_env()
        # Override with test values
        config.twitch.client_id = "test_client_id"
        config.twitch.client_secret = "test_client_secret"
        config.twitch.bot_username = "TestBot"
        config.twitch.streamer_username = "TestStreamer"
        config.ai.gemini_api_key = "test_gemini_key"
        config.ai.elevenlabs_api_key = "test_elevenlabs_key"
        return config
    
    @pytest.fixture
    async def bot(self, mock_config):
        """Create a bot instance for testing."""
        with patch('harmony.config.manager.ConfigManager.load_config', return_value=mock_config):
            bot = HarmonyBot()
            await bot.initialize()
            yield bot
            await bot.shutdown()
    
    @pytest.mark.asyncio
    async def test_bot_initialization(self, mock_config):
        """Test that the bot initializes all components correctly."""
        with patch('harmony.config.manager.ConfigManager.load_config', return_value=mock_config):
            bot = HarmonyBot()
            
            # Test initialization
            await bot.initialize()
            
            # Verify core components are initialized
            assert bot.config is not None
            assert bot.event_bus is not None
            assert bot.personality_manager is not None
            assert bot.memory_manager is not None
            
            # Clean up
            await bot.shutdown()
    
    @pytest.mark.asyncio
    async def test_event_bus_functionality(self):
        """Test that the event bus works correctly."""
        event_bus = EventBus()
        await event_bus.start()
        
        # Test event subscription and emission
        received_events = []
        
        async def test_handler(event):
            received_events.append(event)
        
        event_bus.subscribe_async(EventType.CHAT_MESSAGE, test_handler)
        
        # Emit test event
        test_event = Event(
            type=EventType.CHAT_MESSAGE,
            data={"message": "test", "username": "testuser"},
            source="test"
        )
        
        await event_bus.emit(test_event)
        
        # Wait for event processing
        await asyncio.sleep(0.1)
        
        # Verify event was received
        assert len(received_events) == 1
        assert received_events[0].data["message"] == "test"
        
        await event_bus.stop()


class TestTwitchIntegration:
    """Integration tests for Twitch client functionality."""
    
    @pytest.mark.asyncio
    async def test_twitch_message_processing(self):
        """Test that Twitch messages are processed correctly."""
        from harmony.twitch.client import TwitchClient
        from harmony.config.settings import TwitchConfig
        
        # Mock configuration
        config = TwitchConfig(
            client_id="test_id",
            client_secret="test_secret",
            bot_username="TestBot",
            streamer_username="TestStreamer"
        )
        
        event_bus = EventBus()
        await event_bus.start()
        
        # Track emitted events
        emitted_events = []
        
        async def event_tracker(event):
            emitted_events.append(event)
        
        event_bus.subscribe_async(EventType.CHAT_MESSAGE, event_tracker)
        
        # Mock TwitchIO components
        with patch('harmony.twitch.client.TwitchClient._authenticate') as mock_auth, \
             patch('harmony.twitch.client.TwitchClient._initialize_bot') as mock_init:
            
            mock_auth.return_value = True
            
            client = TwitchClient(config, event_bus)
            await client.initialize()
            
            # Simulate message processing
            mock_message = MagicMock()
            mock_message.echo = False
            mock_message.author.name = "testuser"
            mock_message.content = "Hello bot!"
            mock_message.channel.name = "testchannel"
            mock_message.author.id = "12345"
            mock_message.author.is_mod = False
            mock_message.author.is_subscriber = True
            
            # Simulate event_message handler
            await event_bus.emit(Event(
                type=EventType.CHAT_MESSAGE,
                data={
                    "username": mock_message.author.name,
                    "message": mock_message.content,
                    "channel": mock_message.channel.name,
                    "timestamp": datetime.now().isoformat(),
                    "user_id": mock_message.author.id,
                    "is_mod": mock_message.author.is_mod,
                    "is_subscriber": mock_message.author.is_subscriber
                },
                source="twitch_client"
            ))
            
            # Wait for event processing
            await asyncio.sleep(0.1)
            
            # Verify event was emitted
            assert len(emitted_events) == 1
            assert emitted_events[0].data["message"] == "Hello bot!"
            assert emitted_events[0].data["username"] == "testuser"
        
        await event_bus.stop()


class TestAIIntegration:
    """Integration tests for AI engine functionality."""
    
    @pytest.mark.asyncio
    async def test_ai_response_generation(self):
        """Test that AI generates responses to chat messages."""
        from harmony.ai.llm_client import AIEngine
        from harmony.config.settings import AIConfig
        
        # Mock configuration
        config = AIConfig(
            gemini_api_key="test_key",
            model="gemini-2.0-flash-001"
        )
        
        event_bus = EventBus()
        await event_bus.start()
        
        # Track AI responses
        ai_responses = []
        
        async def response_tracker(event):
            ai_responses.append(event)
        
        event_bus.subscribe_async(EventType.AI_RESPONSE, response_tracker)
        
        # Mock Google Gemini
        with patch('harmony.ai.llm_client.genai') as mock_genai:
            mock_model = MagicMock()
            mock_response = MagicMock()
            mock_response.text = "Hello there! How can I help you?"
            mock_model.generate_content.return_value = mock_response
            mock_genai.GenerativeModel.return_value = mock_model
            
            ai_engine = AIEngine(config, event_bus)
            await ai_engine.initialize()
            
            # Simulate chat message that should trigger AI response
            chat_event = Event(
                type=EventType.CHAT_MESSAGE,
                data={
                    "username": "testuser",
                    "message": "@HarmonyBot hello",
                    "channel": "testchannel",
                    "timestamp": datetime.now().isoformat()
                },
                source="twitch_client"
            )
            
            await ai_engine._handle_chat_message(chat_event)
            
            # Wait for processing
            await asyncio.sleep(0.1)
            
            # Verify AI response was generated
            assert len(ai_responses) == 1
            assert ai_responses[0].data["response"] == "Hello there! How can I help you?"
            assert ai_responses[0].data["username"] == "testuser"
        
        await event_bus.stop()


class TestAudioIntegration:
    """Integration tests for audio/TTS functionality."""
    
    @pytest.mark.asyncio
    async def test_tts_audio_generation(self):
        """Test that TTS generates audio from AI responses."""
        from harmony.audio.tts import AudioProcessor
        from harmony.config.settings import AudioConfig
        
        # Mock configuration
        config = AudioConfig(
            elevenlabs_api_key="test_key",
            voice_id="test_voice",
            enable_tts=True
        )
        
        event_bus = EventBus()
        await event_bus.start()
        
        # Track audio generation
        audio_events = []
        
        async def audio_tracker(event):
            audio_events.append(event)
        
        event_bus.subscribe_async(EventType.AUDIO_GENERATED, audio_tracker)
        
        # Mock ElevenLabs
        with patch('harmony.audio.tts.ElevenLabs') as mock_elevenlabs:
            mock_client = MagicMock()
            mock_voice = MagicMock()
            mock_voice.name = "test_voice"
            
            # Mock audio generation
            mock_audio_data = b"fake_audio_data"
            mock_client.generate.return_value = [mock_audio_data]
            mock_elevenlabs.return_value = mock_client
            
            with patch('harmony.audio.tts.Voice', return_value=mock_voice):
                audio_processor = AudioProcessor(config, event_bus)
                await audio_processor.initialize()
                
                # Simulate AI response that should trigger TTS
                ai_response_event = Event(
                    type=EventType.AI_RESPONSE,
                    data={
                        "response": "Hello! This is a test response.",
                        "username": "testuser",
                        "channel": "testchannel"
                    },
                    source="ai_engine"
                )
                
                await audio_processor._handle_ai_response(ai_response_event)
                
                # Wait for processing
                await asyncio.sleep(0.2)
                
                # Verify audio was generated
                assert len(audio_events) >= 1
                assert audio_events[0].data["text"] == "Hello! This is a test response."
        
        await event_bus.stop()


class TestEndToEndIntegration:
    """End-to-end integration tests."""
    
    @pytest.mark.asyncio
    async def test_full_message_flow(self):
        """Test complete message flow: Chat -> AI -> TTS -> Audio."""
        # This test simulates the complete flow without external APIs
        
        event_bus = EventBus()
        await event_bus.start()
        
        # Track all events in the flow
        chat_events = []
        ai_events = []
        audio_events = []
        
        async def chat_tracker(event):
            chat_events.append(event)
        
        async def ai_tracker(event):
            ai_events.append(event)
        
        async def audio_tracker(event):
            audio_events.append(event)
        
        event_bus.subscribe_async(EventType.CHAT_MESSAGE, chat_tracker)
        event_bus.subscribe_async(EventType.AI_RESPONSE, ai_tracker)
        event_bus.subscribe_async(EventType.AUDIO_GENERATED, audio_tracker)
        
        # Simulate the complete flow
        
        # 1. Chat message arrives
        chat_event = Event(
            type=EventType.CHAT_MESSAGE,
            data={
                "username": "testuser",
                "message": "@HarmonyBot how are you?",
                "channel": "testchannel",
                "timestamp": datetime.now().isoformat()
            },
            source="twitch_client"
        )
        await event_bus.emit(chat_event)
        
        # 2. AI responds
        ai_event = Event(
            type=EventType.AI_RESPONSE,
            data={
                "response": "I'm doing great! Thanks for asking!",
                "original_message": "@HarmonyBot how are you?",
                "username": "testuser",
                "channel": "testchannel"
            },
            source="ai_engine"
        )
        await event_bus.emit(ai_event)
        
        # 3. Audio is generated
        audio_event = Event(
            type=EventType.AUDIO_GENERATED,
            data={
                "text": "I'm doing great! Thanks for asking!",
                "audio_size": 1024,
                "voice": "test_voice"
            },
            source="audio_processor"
        )
        await event_bus.emit(audio_event)
        
        # Wait for all events to be processed
        await asyncio.sleep(0.1)
        
        # Verify the complete flow
        assert len(chat_events) == 1
        assert len(ai_events) == 1
        assert len(audio_events) == 1
        
        # Verify data integrity through the flow
        assert chat_events[0].data["message"] == "@HarmonyBot how are you?"
        assert ai_events[0].data["response"] == "I'm doing great! Thanks for asking!"
        assert audio_events[0].data["text"] == "I'm doing great! Thanks for asking!"
        
        await event_bus.stop()


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
