#!/usr/bin/env python3
"""
Unit tests for Configuration Manager.
"""

import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import Mock, patch

from harmony.config.manager import ConfigManager
from harmony.config.settings import HarmonyConfig, TwitchConfig, AIConfig, SecurityConfig


class TestConfigManager:
    """Test cases for ConfigManager class."""

    @pytest.fixture
    def temp_config_dir(self):
        """Create temporary config directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def sample_config_data(self):
        """Create sample configuration data."""
        return {
            "twitch": {
                "client_id": "test_client_id",
                "client_secret": "test_client_secret",
                "channel": "test_channel",
                "bot_username": "test_bot"
            },
            "ai": {
                "gemini_api_key": "test_gemini_key",
                "elevenlabs_api_key": "test_elevenlabs_key"
            },
            "audio": {
                "enabled": True,
                "volume": 0.8
            }
        }

    @pytest.fixture
    def config_manager(self, temp_config_dir):
        """Create ConfigManager instance with temp directory."""
        config_path = temp_config_dir / "config.yaml"
        return ConfigManager(config_path=str(config_path))

    def test_config_manager_initialization(self, config_manager, temp_config_dir):
        """Test ConfigManager initialization."""
        config_path = temp_config_dir / "config.yaml"
        assert config_manager.config_path == str(config_path)

    def test_load_config_file_exists(self, config_manager, sample_config_data):
        """Test loading configuration when file exists."""
        # Create config file
        with open(config_manager.config_path, 'w') as f:
            yaml.dump(sample_config_data, f)

        config = config_manager.load_config()
        
        assert isinstance(config, HarmonyConfig)
        assert config.twitch.client_id == "test_client_id"
        assert config.ai.gemini_api_key == "test_gemini_key"

    def test_load_config_file_not_exists(self, config_manager):
        """Test loading configuration when file doesn't exist."""
        config = config_manager.load_config()
        
        # Should return default configuration
        assert isinstance(config, HarmonyConfig)
        assert config.twitch.client_id == ""  # Default empty value

    def test_save_config(self, config_manager, sample_config_data):
        """Test saving configuration."""
        # Create config object
        config = HarmonyConfig(
            twitch=TwitchConfig(
                client_id="test_client_id",
                client_secret="test_client_secret",
                streamer_username="test_streamer",
                bot_username="test_bot"
            ),
            ai=AIConfig(
                gemini_api_key="test_gemini_key",
                elevenlabs_api_key="test_elevenlabs_key"
            ),
            security=SecurityConfig(
                secret_key="test_secret_key",
                token_encryption_password="test_password"
            )
        )

        # Save config
        config_manager.save_config(config)

        # Verify file was created
        assert config_manager.config_file.exists()

        # Load and verify content
        loaded_config = config_manager.load_config()
        assert loaded_config.twitch.client_id == "test_client_id"
        assert loaded_config.ai.gemini_api_key == "test_gemini_key"

    def test_validate_config_valid(self, config_manager):
        """Test configuration validation with valid config."""
        config = HarmonyConfig(
            twitch=TwitchConfig(
                client_id="valid_client_id",
                client_secret="valid_secret",
                streamer_username="valid_streamer",
                bot_username="valid_bot"
            ),
            ai=AIConfig(
                gemini_api_key="valid_gemini_key",
                elevenlabs_api_key="valid_elevenlabs_key"
            ),
            security=SecurityConfig(
                secret_key="valid_secret_key",
                token_encryption_password="valid_password"
            )
        )

        is_valid, errors = config_manager.validate_config(config)
        assert is_valid is True
        assert len(errors) == 0

    def test_validate_config_invalid(self, config_manager):
        """Test configuration validation with invalid config."""
        config = HarmonyConfig(
            twitch=TwitchConfig(
                client_id="",  # Invalid: empty
                client_secret="",  # Invalid: empty
                streamer_username="",  # Invalid: empty
                bot_username=""  # Invalid: empty
            ),
            ai=AIConfig(
                gemini_api_key="",  # Invalid: empty
                elevenlabs_api_key=""  # Invalid: empty
            ),
            security=SecurityConfig(
                secret_key="",  # Invalid: empty
                token_encryption_password=""  # Invalid: empty
            )
        )

        is_valid, errors = config_manager.validate_config(config)
        assert is_valid is False
        assert len(errors) > 0

    def test_backup_config(self, config_manager, sample_config_data):
        """Test configuration backup functionality."""
        # Create original config file
        with open(config_manager.config_file, 'w') as f:
            yaml.dump(sample_config_data, f)

        # Create backup
        backup_path = config_manager.backup_config()

        assert backup_path.exists()
        assert backup_path.name.startswith("config_backup_")
        assert backup_path.suffix == ".yaml"

        # Verify backup content
        with open(backup_path, 'r') as f:
            backup_data = yaml.safe_load(f)
        
        assert backup_data == sample_config_data

    def test_restore_config(self, config_manager, sample_config_data):
        """Test configuration restore functionality."""
        # Create backup file
        backup_path = config_manager.config_dir / "test_backup.yaml"
        with open(backup_path, 'w') as f:
            yaml.dump(sample_config_data, f)

        # Restore from backup
        success = config_manager.restore_config(backup_path)
        assert success is True

        # Verify restored content
        config = config_manager.load_config()
        assert config.twitch.client_id == "test_client_id"

    def test_get_config_template(self, config_manager):
        """Test getting configuration template."""
        template = config_manager.get_config_template()
        
        assert isinstance(template, dict)
        assert "twitch" in template
        assert "ai" in template
        assert "audio" in template

    def test_config_file_permissions(self, config_manager, sample_config_data):
        """Test that config files have appropriate permissions."""
        # Create config file
        with open(config_manager.config_file, 'w') as f:
            yaml.dump(sample_config_data, f)

        # Check file exists and is readable
        assert config_manager.config_file.exists()
        assert config_manager.config_file.is_file()

    def test_invalid_yaml_handling(self, config_manager):
        """Test handling of invalid YAML files."""
        # Create invalid YAML file
        with open(config_manager.config_file, 'w') as f:
            f.write("invalid: yaml: content: [")

        # Should handle gracefully and return default config
        config = config_manager.load_config()
        assert isinstance(config, HarmonyConfig)

    def test_config_directory_creation(self, temp_config_dir):
        """Test that config directory is created if it doesn't exist."""
        # Remove the directory
        import shutil
        shutil.rmtree(temp_config_dir)
        
        # Create config manager - should recreate directory
        config_path = temp_config_dir / "config.yaml"
        config_manager = ConfigManager(config_path=str(config_path))
        config_manager.load_config()
        
        assert temp_config_dir.exists()
        assert temp_config_dir.is_dir()

    @patch.dict('os.environ', {'GOOGLE_API_KEY': 'env_gemini_key'})
    def test_environment_variable_override(self, config_manager):
        """Test that environment variables override config values."""
        config = config_manager.load_config()
        
        # Environment variable should override default
        assert config.ai.gemini_api_key == "env_gemini_key"
