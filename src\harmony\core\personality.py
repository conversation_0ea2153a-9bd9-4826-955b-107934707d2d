"""
Personality management for the AI bot.
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from ..config.settings import PersonalityConfig, PersonalityTrait, SpeakingStyle, VoiceSettings
from ..utils.logger import get_logger

logger = get_logger(__name__)


class PersonalityState(str, Enum):
    """Current personality state/mood."""
    NEUTRAL = "neutral"
    EXCITED = "excited"
    CALM = "calm"
    FOCUSED = "focused"
    PLAYFUL = "playful"
    SUPPORTIVE = "supportive"


@dataclass
class PersonalityContext:
    """Context information that influences personality responses."""
    current_game: Optional[str] = None
    stream_mood: str = "neutral"
    viewer_count: int = 0
    recent_events: List[str] = None
    time_of_day: str = "day"
    streamer_energy: str = "normal"
    
    def __post_init__(self):
        if self.recent_events is None:
            self.recent_events = []


class PersonalityManager:
    """
    Manages the AI's personality traits, speaking style, and adaptive behavior.
    """
    
    def __init__(self, config: PersonalityConfig):
        """
        Initialize the personality manager.
        
        Args:
            config: Personality configuration
        """
        self.config = config
        self.current_state = PersonalityState.NEUTRAL
        self.context = PersonalityContext()
        self.response_history = []
        self.trait_weights = self._calculate_trait_weights()
        
        logger.info(f"Initialized personality: {config.name} with traits: {config.traits}")
    
    def _calculate_trait_weights(self) -> Dict[PersonalityTrait, float]:
        """Calculate weights for each personality trait."""
        weights = {}
        total_traits = len(self.config.traits)
        
        if total_traits == 0:
            return weights
        
        # Equal weight for all configured traits
        base_weight = 1.0 / total_traits
        
        for trait in self.config.traits:
            weights[trait] = base_weight
        
        return weights
    
    def update_config(self, new_config: PersonalityConfig) -> None:
        """
        Update personality configuration.
        
        Args:
            new_config: New personality configuration
        """
        self.config = new_config
        self.trait_weights = self._calculate_trait_weights()
        logger.info(f"Updated personality: {new_config.name}")
    
    def update_context(self, **kwargs) -> None:
        """
        Update personality context.
        
        Args:
            **kwargs: Context updates
        """
        for key, value in kwargs.items():
            if hasattr(self.context, key):
                setattr(self.context, key, value)
                logger.debug(f"Updated personality context: {key} = {value}")
    
    def set_state(self, state: PersonalityState) -> None:
        """
        Set the current personality state.
        
        Args:
            state: New personality state
        """
        if self.current_state != state:
            logger.debug(f"Personality state changed: {self.current_state} -> {state}")
            self.current_state = state
    
    def get_system_prompt(self) -> str:
        """
        Generate a system prompt based on current personality configuration.
        
        Returns:
            System prompt for the AI
        """
        if self.config.system_prompt:
            base_prompt = self.config.system_prompt
        else:
            base_prompt = self._generate_default_system_prompt()
        
        # Add context-aware modifications
        context_additions = self._get_context_prompt_additions()
        
        if context_additions:
            base_prompt += f"\n\nCurrent context: {context_additions}"
        
        return base_prompt
    
    def _generate_default_system_prompt(self) -> str:
        """Generate a default system prompt based on traits."""
        trait_descriptions = {
            PersonalityTrait.CHEERFUL: "upbeat and positive",
            PersonalityTrait.SASSY: "witty and playfully sarcastic",
            PersonalityTrait.ANALYTICAL: "logical and detail-oriented",
            PersonalityTrait.CALM: "peaceful and measured",
            PersonalityTrait.ENERGETIC: "enthusiastic and high-energy",
            PersonalityTrait.HELPFUL: "supportive and eager to assist",
            PersonalityTrait.HUMOROUS: "funny and entertaining",
            PersonalityTrait.PROFESSIONAL: "formal and business-like"
        }
        
        trait_list = [trait_descriptions.get(trait, trait.value) for trait in self.config.traits]
        traits_text = ", ".join(trait_list)
        
        formality = self.config.speaking_style.formality
        verbosity = self.config.speaking_style.verbosity
        
        prompt = f"""You are {self.config.name}, an AI co-host for a Twitch stream. 
Your personality is {traits_text}. 

Speaking style:
- Formality: {formality}
- Response length: {verbosity}
- Emoji usage: {self.config.speaking_style.emoji_usage}

Your role is to engage with chat, support the streamer, and create an entertaining atmosphere. 
Be conversational and adapt to the stream's mood and energy."""
        
        return prompt
    
    def _get_context_prompt_additions(self) -> str:
        """Get context-specific prompt additions."""
        additions = []
        
        if self.context.current_game:
            additions.append(f"Currently playing: {self.context.current_game}")
        
        if self.context.stream_mood != "neutral":
            additions.append(f"Stream mood: {self.context.stream_mood}")
        
        if self.context.viewer_count > 0:
            additions.append(f"Viewer count: {self.context.viewer_count}")
        
        if self.current_state != PersonalityState.NEUTRAL:
            additions.append(f"Current state: {self.current_state.value}")
        
        return "; ".join(additions)
    
    def adapt_response_style(self, message_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adapt response style based on context and personality.
        
        Args:
            message_context: Context about the message being responded to
            
        Returns:
            Style parameters for response generation
        """
        style = {
            "temperature": 0.7,
            "max_tokens": 150,
            "tone": "friendly",
            "emoji_probability": 0.3
        }
        
        # Adjust based on personality traits
        if PersonalityTrait.ENERGETIC in self.config.traits:
            style["temperature"] += 0.1
            style["emoji_probability"] += 0.2
        
        if PersonalityTrait.CALM in self.config.traits:
            style["temperature"] -= 0.1
            style["tone"] = "calm"
        
        if PersonalityTrait.ANALYTICAL in self.config.traits:
            style["max_tokens"] += 50
            style["emoji_probability"] -= 0.1
        
        if PersonalityTrait.HUMOROUS in self.config.traits:
            style["temperature"] += 0.1
        
        # Adjust based on speaking style
        verbosity_multipliers = {
            "brief": 0.7,
            "moderate": 1.0,
            "detailed": 1.5
        }
        multiplier = verbosity_multipliers.get(self.config.speaking_style.verbosity, 1.0)
        style["max_tokens"] = int(style["max_tokens"] * multiplier)
        
        # Adjust emoji usage
        emoji_levels = {
            "none": 0.0,
            "rare": 0.1,
            "moderate": 0.3,
            "frequent": 0.6
        }
        style["emoji_probability"] = emoji_levels.get(
            self.config.speaking_style.emoji_usage, 
            style["emoji_probability"]
        )
        
        # Adjust based on current state
        if self.current_state == PersonalityState.EXCITED:
            style["temperature"] += 0.2
            style["emoji_probability"] += 0.2
        elif self.current_state == PersonalityState.CALM:
            style["temperature"] -= 0.1
            style["emoji_probability"] -= 0.1
        
        # Adjust based on context
        if self.context.viewer_count > 100:
            style["energy"] = "high"
        elif self.context.viewer_count < 10:
            style["energy"] = "intimate"
        
        return style
    
    def should_respond_to_message(self, message: str, username: str, context: Dict[str, Any]) -> bool:
        """
        Determine if the bot should respond to a message based on personality.
        
        Args:
            message: The chat message
            username: Username of the sender
            context: Additional context
            
        Returns:
            True if bot should respond
        """
        # Always respond to direct mentions
        if f"@{self.config.name.lower()}" in message.lower():
            return True
        
        # Respond to questions
        if "?" in message:
            return True
        
        # Personality-based response probability
        base_probability = 0.1  # 10% base chance
        
        if PersonalityTrait.HELPFUL in self.config.traits:
            base_probability += 0.1
        
        if PersonalityTrait.ENERGETIC in self.config.traits:
            base_probability += 0.1
        
        if self.current_state == PersonalityState.EXCITED:
            base_probability += 0.2
        
        # Reduce probability if we've responded recently
        recent_responses = len([r for r in self.response_history[-10:] if r.get("type") == "chat_response"])
        if recent_responses > 3:
            base_probability *= 0.5
        
        import random
        return random.random() < base_probability
    
    def record_response(self, response_type: str, content: str, context: Dict[str, Any]) -> None:
        """
        Record a response for personality learning and adaptation.
        
        Args:
            response_type: Type of response
            content: Response content
            context: Response context
        """
        response_record = {
            "type": response_type,
            "content": content,
            "context": context,
            "timestamp": time.time(),
            "state": self.current_state.value
        }
        
        self.response_history.append(response_record)
        
        # Keep only recent history
        if len(self.response_history) > 100:
            self.response_history = self.response_history[-100:]
    
    def get_voice_settings(self) -> VoiceSettings:
        """
        Get voice settings adjusted for current personality state.
        
        Returns:
            Voice settings for TTS
        """
        settings = self.config.voice_settings.copy()
        
        # Adjust based on current state
        if self.current_state == PersonalityState.EXCITED:
            settings.speed = min(settings.speed * 1.1, 4.0)
            settings.style = min(settings.style + 0.1, 1.0)
        elif self.current_state == PersonalityState.CALM:
            settings.speed = max(settings.speed * 0.9, 0.25)
            settings.stability = min(settings.stability + 0.1, 1.0)
        
        return settings
    
    def get_personality_stats(self) -> Dict[str, Any]:
        """Get personality statistics and current state."""
        return {
            "name": self.config.name,
            "traits": [trait.value for trait in self.config.traits],
            "current_state": self.current_state.value,
            "context": {
                "current_game": self.context.current_game,
                "stream_mood": self.context.stream_mood,
                "viewer_count": self.context.viewer_count
            },
            "response_count": len(self.response_history),
            "trait_weights": {trait.value: weight for trait, weight in self.trait_weights.items()}
        }


# Import time for response recording
import time
