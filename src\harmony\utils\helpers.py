"""
General utility functions and helpers for Harmony bot.
"""

import asyncio
import time
import re
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime, timedelta
from functools import wraps
from collections import defaultdict

from .logger import get_logger

logger = get_logger(__name__)


def format_uptime(seconds: int) -> str:
    """
    Format uptime seconds into a human-readable string.
    
    Args:
        seconds: Uptime in seconds
        
    Returns:
        Formatted uptime string
    """
    if seconds < 60:
        return f"{seconds}s"
    
    minutes = seconds // 60
    if minutes < 60:
        return f"{minutes}m {seconds % 60}s"
    
    hours = minutes // 60
    if hours < 24:
        return f"{hours}h {minutes % 60}m"
    
    days = hours // 24
    return f"{days}d {hours % 24}h {minutes % 60}m"


def sanitize_message(message: str, max_length: int = 500) -> str:
    """
    Sanitize a chat message by removing unwanted characters and limiting length.
    
    Args:
        message: Message to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized message
    """
    if not message:
        return ""
    
    # Remove control characters and excessive whitespace
    message = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', message)
    message = re.sub(r'\s+', ' ', message)
    message = message.strip()
    
    # Limit length
    if len(message) > max_length:
        message = message[:max_length-3] + "..."
    
    return message


def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
    """
    Truncate text to a maximum length with optional suffix.
    
    Args:
        text: Text to truncate
        max_length: Maximum length including suffix
        suffix: Suffix to add when truncating
        
    Returns:
        Truncated text
    """
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def parse_duration(duration_str: str) -> Optional[int]:
    """
    Parse a duration string into seconds.
    
    Args:
        duration_str: Duration string (e.g., "5m", "1h30m", "2d")
        
    Returns:
        Duration in seconds or None if invalid
    """
    if not duration_str:
        return None
    
    # Pattern to match duration components
    pattern = r'(?:(\d+)d)?(?:(\d+)h)?(?:(\d+)m)?(?:(\d+)s)?'
    match = re.match(pattern, duration_str.lower().strip())
    
    if not match:
        return None
    
    days, hours, minutes, seconds = match.groups()
    
    total_seconds = 0
    if days:
        total_seconds += int(days) * 86400
    if hours:
        total_seconds += int(hours) * 3600
    if minutes:
        total_seconds += int(minutes) * 60
    if seconds:
        total_seconds += int(seconds)
    
    return total_seconds if total_seconds > 0 else None


def format_timestamp(timestamp: datetime, format_type: str = "relative") -> str:
    """
    Format a timestamp into a human-readable string.
    
    Args:
        timestamp: Timestamp to format
        format_type: Format type ("relative", "absolute", "time_only")
        
    Returns:
        Formatted timestamp string
    """
    if format_type == "absolute":
        return timestamp.strftime("%Y-%m-%d %H:%M:%S")
    elif format_type == "time_only":
        return timestamp.strftime("%H:%M:%S")
    else:  # relative
        now = datetime.now()
        diff = now - timestamp
        
        if diff.total_seconds() < 60:
            return "just now"
        elif diff.total_seconds() < 3600:
            minutes = int(diff.total_seconds() // 60)
            return f"{minutes}m ago"
        elif diff.total_seconds() < 86400:
            hours = int(diff.total_seconds() // 3600)
            return f"{hours}h ago"
        else:
            days = int(diff.total_seconds() // 86400)
            return f"{days}d ago"


def extract_mentions(message: str) -> List[str]:
    """
    Extract @mentions from a message.
    
    Args:
        message: Message to extract mentions from
        
    Returns:
        List of mentioned usernames (without @)
    """
    if not message:
        return []
    
    # Pattern to match @username (alphanumeric + underscore)
    pattern = r'@([a-zA-Z0-9_]+)'
    matches = re.findall(pattern, message)
    
    return list(set(matches))  # Remove duplicates


def extract_urls(message: str) -> List[str]:
    """
    Extract URLs from a message.
    
    Args:
        message: Message to extract URLs from
        
    Returns:
        List of URLs found in the message
    """
    if not message:
        return []
    
    # Pattern to match URLs
    pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
    matches = re.findall(pattern, message)
    
    return matches


def rate_limit(calls_per_minute: int):
    """
    Decorator to rate limit function calls.
    
    Args:
        calls_per_minute: Maximum number of calls per minute
        
    Returns:
        Decorated function
    """
    call_times = []
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            now = time.time()
            
            # Remove calls older than 1 minute
            call_times[:] = [t for t in call_times if now - t < 60]
            
            # Check if we've exceeded the rate limit
            if len(call_times) >= calls_per_minute:
                sleep_time = 60 - (now - call_times[0])
                if sleep_time > 0:
                    logger.warning(f"Rate limit exceeded, sleeping for {sleep_time:.1f}s")
                    await asyncio.sleep(sleep_time)
            
            # Record this call
            call_times.append(now)
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    Split a list into chunks of specified size.
    
    Args:
        lst: List to chunk
        chunk_size: Size of each chunk
        
    Returns:
        List of chunks
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def safe_get(dictionary: Dict[str, Any], key: str, default: Any = None) -> Any:
    """
    Safely get a value from a nested dictionary using dot notation.
    
    Args:
        dictionary: Dictionary to search
        key: Key in dot notation (e.g., "user.profile.name")
        default: Default value if key not found
        
    Returns:
        Value or default
    """
    keys = key.split('.')
    value = dictionary
    
    try:
        for k in keys:
            value = value[k]
        return value
    except (KeyError, TypeError):
        return default


def merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    Recursively merge two dictionaries.
    
    Args:
        dict1: First dictionary
        dict2: Second dictionary (takes precedence)
        
    Returns:
        Merged dictionary
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_dicts(result[key], value)
        else:
            result[key] = value
    
    return result


def debounce(wait_time: float):
    """
    Decorator to debounce function calls.
    
    Args:
        wait_time: Time to wait before allowing next call
        
    Returns:
        Decorated function
    """
    def decorator(func):
        last_called = [0]
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            now = time.time()
            if now - last_called[0] >= wait_time:
                last_called[0] = now
                return await func(*args, **kwargs)
            else:
                logger.debug(f"Debounced call to {func.__name__}")
        
        return wrapper
    return decorator


def retry_async(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator to retry async function calls with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        delay: Initial delay between retries
        backoff: Backoff multiplier for delay
        
    Returns:
        Decorated function
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        logger.error(f"Function {func.__name__} failed after {max_attempts} attempts: {e}")
                        raise
                    
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. Retrying in {current_delay}s...")
                    await asyncio.sleep(current_delay)
                    current_delay *= backoff
        
        return wrapper
    return decorator


class CircularBuffer:
    """
    A circular buffer implementation for storing recent items.
    """
    
    def __init__(self, size: int):
        """
        Initialize the circular buffer.
        
        Args:
            size: Maximum size of the buffer
        """
        self.size = size
        self.buffer = []
        self.index = 0
    
    def add(self, item: Any) -> None:
        """Add an item to the buffer."""
        if len(self.buffer) < self.size:
            self.buffer.append(item)
        else:
            self.buffer[self.index] = item
            self.index = (self.index + 1) % self.size
    
    def get_all(self) -> List[Any]:
        """Get all items in chronological order."""
        if len(self.buffer) < self.size:
            return self.buffer.copy()
        
        return self.buffer[self.index:] + self.buffer[:self.index]
    
    def get_recent(self, count: int) -> List[Any]:
        """Get the most recent items."""
        all_items = self.get_all()
        return all_items[-count:] if count <= len(all_items) else all_items
    
    def clear(self) -> None:
        """Clear the buffer."""
        self.buffer.clear()
        self.index = 0


class EventCounter:
    """
    Counter for tracking events over time windows.
    """
    
    def __init__(self, window_size: int = 60):
        """
        Initialize the event counter.
        
        Args:
            window_size: Time window in seconds
        """
        self.window_size = window_size
        self.events = defaultdict(list)
    
    def add_event(self, event_type: str) -> None:
        """Add an event of the specified type."""
        now = time.time()
        self.events[event_type].append(now)
        self._cleanup_old_events(event_type, now)
    
    def get_count(self, event_type: str) -> int:
        """Get the count of events in the current window."""
        now = time.time()
        self._cleanup_old_events(event_type, now)
        return len(self.events[event_type])
    
    def get_rate(self, event_type: str) -> float:
        """Get the rate of events per minute."""
        count = self.get_count(event_type)
        return (count / self.window_size) * 60
    
    def _cleanup_old_events(self, event_type: str, current_time: float) -> None:
        """Remove events outside the time window."""
        cutoff_time = current_time - self.window_size
        self.events[event_type] = [
            t for t in self.events[event_type] 
            if t >= cutoff_time
        ]
