#!/usr/bin/env python3
"""
Unit tests for utility helper functions that actually exist.
"""

import pytest
import time
from datetime import datetime, timedelta

from harmony.utils.helpers import (
    format_uptime, sanitize_message, truncate_text, parse_duration,
    format_timestamp, extract_mentions, extract_urls, chunk_list,
    safe_get, merge_dicts, retry_async, CircularBuffer, EventCounter
)


class TestHelpers:
    """Test cases for utility helper functions."""

    def test_format_uptime_seconds(self):
        """Test uptime formatting for seconds."""
        assert format_uptime(30) == "30s"
        assert format_uptime(59) == "59s"

    def test_format_uptime_minutes(self):
        """Test uptime formatting for minutes."""
        assert format_uptime(60) == "1m 0s"
        assert format_uptime(90) == "1m 30s"
        assert format_uptime(3599) == "59m 59s"

    def test_format_uptime_hours(self):
        """Test uptime formatting for hours."""
        assert format_uptime(3600) == "1h 0m"
        assert format_uptime(3661) == "1h 1m"

    def test_format_uptime_days(self):
        """Test uptime formatting for days."""
        assert format_uptime(86400) == "1d 0h 0m"
        assert format_uptime(90061) == "1d 1h 1m"

    def test_sanitize_message_basic(self):
        """Test basic message sanitization."""
        result = sanitize_message("Hello world!")
        assert result == "Hello world!"

    def test_sanitize_message_control_chars(self):
        """Test message sanitization with control characters."""
        message_with_control = "Hello\x00\x1f world"
        result = sanitize_message(message_with_control)
        assert "\x00" not in result
        assert "\x1f" not in result
        assert "Hello world" in result

    def test_sanitize_message_whitespace(self):
        """Test message sanitization with excessive whitespace."""
        result = sanitize_message("Hello    world   ")
        assert result == "Hello world"

    def test_sanitize_message_length_limit(self):
        """Test message sanitization with length limit."""
        long_message = "a" * 600
        result = sanitize_message(long_message, max_length=100)
        assert len(result) <= 100
        assert result.endswith("...")

    def test_sanitize_message_empty(self):
        """Test message sanitization with empty input."""
        assert sanitize_message("") == ""
        assert sanitize_message(None) == ""

    def test_truncate_text_short(self):
        """Test text truncation with short text."""
        text = "Short text"
        result = truncate_text(text, 50)
        assert result == text

    def test_truncate_text_long(self):
        """Test text truncation with long text."""
        text = "This is a very long text that should be truncated"
        result = truncate_text(text, 20)
        assert len(result) <= 20
        assert result.endswith("...")

    def test_truncate_text_exact_length(self):
        """Test text truncation with exact length."""
        text = "Exactly twenty chars"
        result = truncate_text(text, 20)
        assert result == text

    def test_truncate_text_custom_suffix(self):
        """Test text truncation with custom suffix."""
        text = "Long text here"
        result = truncate_text(text, 10, suffix="[...]")
        assert result.endswith("[...]")

    def test_parse_duration_simple(self):
        """Test duration parsing for simple cases."""
        assert parse_duration("30s") == 30
        assert parse_duration("5m") == 300
        assert parse_duration("2h") == 7200
        assert parse_duration("1d") == 86400

    def test_parse_duration_complex(self):
        """Test duration parsing for complex cases."""
        assert parse_duration("1h30m") == 5400  # 3600 + 1800
        # 2d = 172800, 5h = 18000, 30m = 1800, 15s = 15 = 192615
        assert parse_duration("2d5h30m15s") == 192615

    def test_parse_duration_invalid(self):
        """Test duration parsing with invalid input."""
        assert parse_duration("invalid") is None
        assert parse_duration("") is None
        assert parse_duration("123") is None  # No unit

    def test_format_timestamp_absolute(self):
        """Test timestamp formatting in absolute format."""
        dt = datetime(2024, 1, 15, 14, 30, 45)
        result = format_timestamp(dt, "absolute")
        assert result == "2024-01-15 14:30:45"

    def test_format_timestamp_time_only(self):
        """Test timestamp formatting with time only."""
        dt = datetime(2024, 1, 15, 14, 30, 45)
        result = format_timestamp(dt, "time_only")
        assert result == "14:30:45"

    def test_format_timestamp_relative(self):
        """Test timestamp formatting in relative format."""
        now = datetime.now()
        recent = now - timedelta(seconds=30)
        result = format_timestamp(recent, "relative")
        assert result == "just now"

    def test_extract_mentions_basic(self):
        """Test mention extraction from text."""
        text = "Hello @user1 and @user2!"
        mentions = extract_mentions(text)
        assert "user1" in mentions
        assert "user2" in mentions
        assert len(mentions) == 2

    def test_extract_mentions_none(self):
        """Test mention extraction with no mentions."""
        text = "Hello everyone!"
        mentions = extract_mentions(text)
        assert len(mentions) == 0

    def test_extract_mentions_duplicate(self):
        """Test mention extraction with duplicate mentions."""
        text = "Hello @user1 and @user1 again!"
        mentions = extract_mentions(text)
        assert len(mentions) == 1
        assert "user1" in mentions

    def test_extract_urls_basic(self):
        """Test URL extraction from text."""
        text = "Check out https://example.com and http://test.org"
        urls = extract_urls(text)
        assert "https://example.com" in urls
        assert "http://test.org" in urls
        assert len(urls) == 2

    def test_extract_urls_none(self):
        """Test URL extraction with no URLs."""
        text = "No URLs here!"
        urls = extract_urls(text)
        assert len(urls) == 0

    def test_chunk_list_basic(self):
        """Test list chunking."""
        lst = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        chunks = chunk_list(lst, 3)
        assert len(chunks) == 4
        assert chunks[0] == [1, 2, 3]
        assert chunks[1] == [4, 5, 6]  # Fixed: chunks don't overlap
        assert chunks[-1] == [10]

    def test_chunk_list_exact_division(self):
        """Test list chunking with exact division."""
        lst = [1, 2, 3, 4, 5, 6]
        chunks = chunk_list(lst, 2)
        assert len(chunks) == 3
        assert all(len(chunk) == 2 for chunk in chunks)

    def test_safe_get_basic(self):
        """Test safe dictionary access."""
        data = {"user": {"profile": {"name": "John"}}}
        assert safe_get(data, "user.profile.name") == "John"
        assert safe_get(data, "user.profile.age", "Unknown") == "Unknown"

    def test_safe_get_missing_key(self):
        """Test safe dictionary access with missing key."""
        data = {"user": {"profile": {}}}
        assert safe_get(data, "user.profile.missing") is None
        assert safe_get(data, "user.profile.missing", "default") == "default"

    def test_merge_dicts_basic(self):
        """Test basic dictionary merging."""
        dict1 = {"a": 1, "b": 2}
        dict2 = {"c": 3, "d": 4}
        result = merge_dicts(dict1, dict2)
        
        assert result["a"] == 1
        assert result["b"] == 2
        assert result["c"] == 3
        assert result["d"] == 4

    def test_merge_dicts_nested(self):
        """Test nested dictionary merging."""
        dict1 = {"a": {"x": 1, "y": 2}}
        dict2 = {"a": {"z": 3}, "b": 4}
        result = merge_dicts(dict1, dict2)
        
        assert result["a"]["x"] == 1
        assert result["a"]["y"] == 2
        assert result["a"]["z"] == 3
        assert result["b"] == 4

    def test_circular_buffer_basic(self):
        """Test basic circular buffer operations."""
        buffer = CircularBuffer(3)
        
        buffer.add("item1")
        buffer.add("item2")
        buffer.add("item3")
        
        items = buffer.get_all()
        assert len(items) == 3
        assert "item1" in items
        assert "item2" in items
        assert "item3" in items

    def test_circular_buffer_overflow(self):
        """Test circular buffer overflow behavior."""
        buffer = CircularBuffer(2)
        
        buffer.add("item1")
        buffer.add("item2")
        buffer.add("item3")  # Should replace item1
        
        items = buffer.get_all()
        assert len(items) == 2
        assert "item2" in items
        assert "item3" in items
        assert "item1" not in items

    def test_circular_buffer_recent(self):
        """Test getting recent items from circular buffer."""
        buffer = CircularBuffer(5)
        
        for i in range(5):
            buffer.add(f"item{i}")
        
        recent = buffer.get_recent(3)
        assert len(recent) == 3
        assert recent == ["item2", "item3", "item4"]

    def test_event_counter_basic(self):
        """Test basic event counter operations."""
        counter = EventCounter(window_size=60)
        
        counter.add_event("test_event")
        counter.add_event("test_event")
        
        assert counter.get_count("test_event") == 2

    def test_event_counter_rate(self):
        """Test event counter rate calculation."""
        counter = EventCounter(window_size=60)
        
        # Add 10 events
        for _ in range(10):
            counter.add_event("test_event")
        
        rate = counter.get_rate("test_event")
        assert rate == 10.0  # 10 events per minute

    @pytest.mark.asyncio
    async def test_retry_async_success(self):
        """Test async retry decorator with successful function."""
        call_count = 0
        
        @retry_async(max_attempts=3, delay=0.01)
        async def test_func():
            nonlocal call_count
            call_count += 1
            return "success"
        
        result = await test_func()
        assert result == "success"
        assert call_count == 1

    @pytest.mark.asyncio
    async def test_retry_async_eventual_success(self):
        """Test async retry decorator with eventual success."""
        call_count = 0
        
        @retry_async(max_attempts=3, delay=0.01)
        async def test_func():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return "success"
        
        result = await test_func()
        assert result == "success"
        assert call_count == 3
