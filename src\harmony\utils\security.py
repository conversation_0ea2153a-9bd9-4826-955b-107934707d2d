"""
Security utilities for token encryption and secure storage.
"""

import os
import base64
from typing import Op<PERSON>, Union
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from .logger import get_logger

logger = get_logger(__name__)


class SecurityManager:
    """
    Manages encryption and decryption of sensitive data like OAuth tokens.
    """
    
    def __init__(self, password: str, salt: Optional[bytes] = None):
        """
        Initialize the security manager with a password.
        
        Args:
            password: Password for encryption/decryption
            salt: Optional salt for key derivation (generated if not provided)
        """
        self.password = password.encode()
        self.salt = salt or os.urandom(16)
        self._fernet = self._create_fernet()
    
    def _create_fernet(self) -> <PERSON><PERSON><PERSON>:
        """Create a Fernet instance for encryption/decryption."""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.password))
        return Fernet(key)
    
    def encrypt(self, data: Union[str, bytes]) -> str:
        """
        Encrypt data and return base64 encoded string.
        
        Args:
            data: Data to encrypt
            
        Returns:
            Base64 encoded encrypted data
        """
        if isinstance(data, str):
            data = data.encode()
        
        encrypted = self._fernet.encrypt(data)
        return base64.urlsafe_b64encode(encrypted).decode()
    
    def decrypt(self, encrypted_data: str) -> str:
        """
        Decrypt base64 encoded encrypted data.
        
        Args:
            encrypted_data: Base64 encoded encrypted data
            
        Returns:
            Decrypted data as string
        """
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted = self._fernet.decrypt(encrypted_bytes)
        return decrypted.decode()
    
    def get_salt(self) -> str:
        """Get the salt as base64 encoded string."""
        return base64.urlsafe_b64encode(self.salt).decode()
    
    @classmethod
    def from_salt(cls, password: str, salt_b64: str) -> "SecurityManager":
        """
        Create SecurityManager from password and base64 encoded salt.
        
        Args:
            password: Password for encryption/decryption
            salt_b64: Base64 encoded salt
            
        Returns:
            SecurityManager instance
        """
        salt = base64.urlsafe_b64decode(salt_b64.encode())
        return cls(password, salt)


def encrypt_token(token: str, password: str) -> tuple[str, str]:
    """
    Encrypt a token with a password.
    
    Args:
        token: Token to encrypt
        password: Password for encryption
        
    Returns:
        Tuple of (encrypted_token, salt) both as base64 strings
    """
    security_manager = SecurityManager(password)
    encrypted_token = security_manager.encrypt(token)
    salt = security_manager.get_salt()
    return encrypted_token, salt


def decrypt_token(encrypted_token: str, password: str, salt: str) -> str:
    """
    Decrypt a token with a password and salt.
    
    Args:
        encrypted_token: Base64 encoded encrypted token
        password: Password for decryption
        salt: Base64 encoded salt
        
    Returns:
        Decrypted token
    """
    security_manager = SecurityManager.from_salt(password, salt)
    return security_manager.decrypt(encrypted_token)


def generate_secure_key(length: int = 32) -> str:
    """
    Generate a secure random key.
    
    Args:
        length: Length of the key in bytes
        
    Returns:
        Base64 encoded secure key
    """
    key = os.urandom(length)
    return base64.urlsafe_b64encode(key).decode()


def hash_password(password: str, salt: Optional[bytes] = None) -> tuple[str, str]:
    """
    Hash a password with PBKDF2.
    
    Args:
        password: Password to hash
        salt: Optional salt (generated if not provided)
        
    Returns:
        Tuple of (hashed_password, salt) both as base64 strings
    """
    if salt is None:
        salt = os.urandom(16)
    
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    
    hashed = kdf.derive(password.encode())
    return (
        base64.urlsafe_b64encode(hashed).decode(),
        base64.urlsafe_b64encode(salt).decode()
    )


def verify_password(password: str, hashed_password: str, salt: str) -> bool:
    """
    Verify a password against a hash.
    
    Args:
        password: Password to verify
        hashed_password: Base64 encoded hashed password
        salt: Base64 encoded salt
        
    Returns:
        True if password matches
    """
    try:
        expected_hash, _ = hash_password(password, base64.urlsafe_b64decode(salt.encode()))
        return expected_hash == hashed_password
    except Exception as e:
        logger.error(f"Error verifying password: {e}")
        return False


class SecureStorage:
    """
    Secure storage for sensitive configuration data.
    """
    
    def __init__(self, password: str):
        """
        Initialize secure storage with a password.
        
        Args:
            password: Master password for encryption
        """
        self.security_manager = SecurityManager(password)
        self._storage = {}
    
    def store(self, key: str, value: str) -> None:
        """
        Store a value securely.
        
        Args:
            key: Storage key
            value: Value to store
        """
        encrypted_value = self.security_manager.encrypt(value)
        self._storage[key] = encrypted_value
        logger.debug(f"Stored encrypted value for key: {key}")
    
    def retrieve(self, key: str) -> Optional[str]:
        """
        Retrieve a stored value.
        
        Args:
            key: Storage key
            
        Returns:
            Decrypted value or None if not found
        """
        encrypted_value = self._storage.get(key)
        if encrypted_value is None:
            return None
        
        try:
            return self.security_manager.decrypt(encrypted_value)
        except Exception as e:
            logger.error(f"Error decrypting value for key {key}: {e}")
            return None
    
    def delete(self, key: str) -> bool:
        """
        Delete a stored value.
        
        Args:
            key: Storage key
            
        Returns:
            True if key was found and deleted
        """
        if key in self._storage:
            del self._storage[key]
            logger.debug(f"Deleted encrypted value for key: {key}")
            return True
        return False
    
    def list_keys(self) -> list[str]:
        """Get list of stored keys."""
        return list(self._storage.keys())
    
    def clear(self) -> None:
        """Clear all stored values."""
        self._storage.clear()
        logger.debug("Cleared all encrypted storage")


def create_secure_config_value(value: str, password: str) -> dict:
    """
    Create a secure configuration value that can be stored in config files.
    
    Args:
        value: Value to encrypt
        password: Password for encryption
        
    Returns:
        Dictionary with encrypted value and metadata
    """
    encrypted_token, salt = encrypt_token(value, password)
    return {
        "encrypted": True,
        "value": encrypted_token,
        "salt": salt,
        "algorithm": "fernet"
    }


def read_secure_config_value(config_value: dict, password: str) -> str:
    """
    Read a secure configuration value.
    
    Args:
        config_value: Dictionary with encrypted value and metadata
        password: Password for decryption
        
    Returns:
        Decrypted value
    """
    if not config_value.get("encrypted", False):
        return config_value.get("value", "")
    
    encrypted_token = config_value["value"]
    salt = config_value["salt"]
    return decrypt_token(encrypted_token, password, salt)
