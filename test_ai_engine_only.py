#!/usr/bin/env python3
"""
Test just the AI engine to isolate the issue.
"""

import asyncio
import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from harmony.config.manager import ConfigManager
from harmony.core.events import EventBus
from harmony.ai.llm_client import AIEngine
from harmony.core.personality import PersonalityManager
from harmony.utils.logger import get_logger, setup_logging
from harmony.config.settings import LoggingConfig

logger = get_logger(__name__)

async def test_ai_engine():
    """Test AI engine with real configuration."""
    # Setup logging
    log_config = LoggingConfig(level='INFO', enable_console=True)
    setup_logging(log_config)
    
    # Load configuration
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    # Initialize event bus
    event_bus = EventBus()
    await event_bus.start()
    
    try:
        # Create personality manager
        personality_manager = PersonalityManager(config.personality)
        print(f"Personality manager created: {type(personality_manager)}")
        print(f"Available methods: {[m for m in dir(personality_manager) if not m.startswith('_')]}")
        
        # Test the method directly
        try:
            prompt = personality_manager.get_system_prompt()
            print(f"✅ get_system_prompt() works: {prompt[:50]}...")
        except Exception as e:
            print(f"❌ get_system_prompt() failed: {e}")
            return
        
        # Create AI engine
        ai_engine = AIEngine(config.ai, personality_manager, event_bus)
        print(f"AI engine created: {type(ai_engine)}")
        
        # Initialize and start
        await ai_engine.initialize()
        await ai_engine.start()
        print("AI engine started successfully")
        
        # Test response generation
        print("Testing response generation...")
        response = await ai_engine.generate_response(
            message="Hello, how are you today?",
            username="TestUser",
            channel="TestChannel"
        )
        
        if response:
            print(f"✅ Response generated: {response}")
        else:
            print("❌ No response generated")
        
        # Stop the engine
        await ai_engine.stop()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await event_bus.stop()

if __name__ == "__main__":
    asyncio.run(test_ai_engine())
