#!/usr/bin/env python3
"""
Simple setup script for Harmony AI Twitch Co-Host <PERSON><PERSON>.
Uses regular input for better terminal compatibility.
"""

import subprocess
import sys
import os
import venv
from pathlib import Path


def print_step(message):
    """Print a step message."""
    print(f"\n🔄 {message}")


def print_success(message):
    """Print a success message."""
    print(f"✅ {message}")


def print_error(message):
    """Print an error message."""
    print(f"❌ {message}")


def print_warning(message):
    """Print a warning message."""
    print(f"⚠️  {message}")


def run_command(command, description):
    """Run a command and handle errors."""
    print_step(description)
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print_success(f"{description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print_error(f"{description} failed: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is 3.9 or higher."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print_error(f"Python 3.9+ required, but you have {version.major}.{version.minor}")
        return False
    print_success(f"Python {version.major}.{version.minor}.{version.micro} detected")
    return True


def create_virtual_environment():
    """Create a virtual environment if it doesn't exist."""
    venv_path = Path(".venv")
    
    if venv_path.exists():
        print_success("Virtual environment already exists")
        return True
    
    try:
        print_step("Creating virtual environment")
        venv.create(".venv", with_pip=True)
        print_success("Virtual environment created")
        return True
    except Exception as e:
        print_error(f"Failed to create virtual environment: {e}")
        return False


def get_venv_python():
    """Get the path to the Python executable in the virtual environment."""
    if os.name == 'nt':  # Windows
        return Path(".venv/Scripts/python.exe")
    else:  # Unix/Linux/macOS
        return Path(".venv/bin/python")


def install_core_dependencies():
    """Install core dependencies only."""
    python_exe = get_venv_python()
    
    # Upgrade pip first
    if not run_command(f'"{python_exe}" -m pip install --upgrade pip', "Upgrading pip"):
        return False
    
    # Install core dependencies
    if not run_command(f'"{python_exe}" -m pip install -r requirements-core.txt', "Installing core dependencies"):
        return False
    
    return True


def create_directories():
    """Create necessary directories."""
    directories = ["data", "data/logs", "data/audio", "cache", "config"]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print_success("Created necessary directories")


def setup_environment_file():
    """Set up environment file with user input."""
    env_file = Path(".env")
    
    if env_file.exists():
        response = input("\n⚠️  .env file already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print_success("Keeping existing .env file")
            return
    
    print("\n" + "="*50)
    print("🔑 API KEY CONFIGURATION")
    print("="*50)
    print("Please enter your API keys below.")
    print("You can find these keys at:")
    print("• Google Gemini: https://ai.google.dev/")
    print("• ElevenLabs: https://elevenlabs.io/")
    print("• Twitch: https://dev.twitch.tv/console/apps")
    print("\nNote: Your input will be visible. Make sure no one is watching your screen.")
    print("-"*50)
    
    # Required API keys
    print("\n📋 REQUIRED API KEYS:")
    google_api_key = input("Google Gemini API Key: ").strip()
    elevenlabs_api_key = input("ElevenLabs API Key: ").strip()
    twitch_client_id = input("Twitch Client ID: ").strip()
    twitch_client_secret = input("Twitch Client Secret: ").strip()
    
    # Optional API keys
    print("\n📋 OPTIONAL API KEYS (press Enter to skip):")
    perplexity_api_key = input("Perplexity API Key (optional): ").strip()
    
    # Security configuration
    print("\n🔐 SECURITY CONFIGURATION:")
    token_password = input("Token Encryption Password (choose a strong password): ").strip()
    
    # Generate secret key automatically
    import secrets
    secret_key = secrets.token_urlsafe(32)
    print(f"✅ Generated secure secret key automatically")
    
    # Validate required fields
    required_fields = {
        "Google Gemini API Key": google_api_key,
        "ElevenLabs API Key": elevenlabs_api_key,
        "Twitch Client ID": twitch_client_id,
        "Twitch Client Secret": twitch_client_secret,
        "Token Encryption Password": token_password
    }
    
    missing_fields = [name for name, value in required_fields.items() if not value]
    if missing_fields:
        print_error(f"Missing required fields: {', '.join(missing_fields)}")
        print("Please run the script again and provide all required information.")
        return False
    
    # Create .env content
    env_content = f"""# Google Gemini API Configuration
GOOGLE_API_KEY={google_api_key}

# ElevenLabs TTS Configuration
ELEVENLABS_API_KEY={elevenlabs_api_key}

# Twitch Application Configuration
TWITCH_CLIENT_ID={twitch_client_id}
TWITCH_CLIENT_SECRET={twitch_client_secret}
TWITCH_REDIRECT_URI=http://localhost:8080/auth/callback

# Optional: Perplexity API for Research
PERPLEXITY_API_KEY={perplexity_api_key}

# Optional: Ollama Configuration (for local models)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# Database Configuration
DATABASE_URL=sqlite:///data/harmony.db

# Security Configuration
SECRET_KEY={secret_key}
TOKEN_ENCRYPTION_PASSWORD={token_password}

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=data/logs/harmony.log

# Audio Configuration
AUDIO_INPUT_DEVICE=default
AUDIO_OUTPUT_DEVICE=default
AUDIO_SAMPLE_RATE=44100

# Feature Toggles
ENABLE_TTS=true
ENABLE_STT=false
ENABLE_TRIVIA=true
ENABLE_MODERATION=true
ENABLE_GOAL_TRACKING=true
ENABLE_REMINDERS=true

# Development Settings
DEBUG=false
DEVELOPMENT_MODE=false
MOCK_TWITCH_API=false
"""
    
    env_file.write_text(env_content)
    print_success("Created .env file with your configuration")
    return True


def verify_installation():
    """Verify that key packages are installed."""
    python_exe = get_venv_python()
    
    packages_to_check = [
        ("google.genai", "Google Gemini"),
        ("elevenlabs", "ElevenLabs"), 
        ("twitchio", "TwitchIO"),
        ("pydantic", "Pydantic")
    ]
    
    print_step("Verifying installation")
    
    for package, name in packages_to_check:
        try:
            result = subprocess.run(
                [str(python_exe), "-c", f"import {package}"],
                capture_output=True,
                text=True,
                check=True
            )
            print(f"  ✅ {name}")
        except subprocess.CalledProcessError:
            print(f"  ❌ {name}")
            return False
    
    return True


def main():
    """Main setup process."""
    print("🚀 Harmony AI Twitch Co-Host Bot - Simple Setup")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create virtual environment
    if not create_virtual_environment():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install core dependencies
    if not install_core_dependencies():
        print_error("Failed to install dependencies")
        print("You may need to install them manually:")
        print("1. Activate virtual environment")
        print("2. Run: pip install -r requirements-core.txt")
        sys.exit(1)
    
    # Setup environment file
    if not setup_environment_file():
        sys.exit(1)
    
    # Verify installation
    if verify_installation():
        print("\n" + "="*60)
        print("🎉 SETUP COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("\nNext steps:")
        print("1. Activate virtual environment:")
        if os.name == 'nt':
            print("   .venv\\Scripts\\activate")
        else:
            print("   source .venv/bin/activate")
        print("2. Test the installation:")
        print("   python run.py --help")
        print("3. Start the bot:")
        print("   python run.py")
        print("\nFor GUI mode:")
        print("   python run.py --gui")
        print("\nFor debug mode:")
        print("   python run.py --debug")
    else:
        print_error("Installation verification failed")
        print("Some packages may not be installed correctly.")
        print("Try running: pip install -r requirements-core.txt")


if __name__ == "__main__":
    main()
