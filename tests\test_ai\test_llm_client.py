#!/usr/bin/env python3
"""
Unit tests for AI Engine and LLM Client.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any

from harmony.ai.llm_client import AIEngine
from harmony.config.settings import AI<PERSON>onfig, PersonalityConfig, PersonalityTrait
from harmony.core.personality import PersonalityManager
from harmony.core.events import EventBus, Event, EventType


class TestAIEngine:
    """Test cases for AIEngine class."""

    @pytest.fixture
    def ai_config(self):
        """Create test AI configuration."""
        return AIConfig(
            gemini_api_key="test_gemini_key",
            gemini_model="gemini-pro",
            elevenlabs_api_key="test_elevenlabs_key",
            ollama_enabled=True,
            ollama_model="llama2",
            ollama_url="http://localhost:11434"
        )

    @pytest.fixture
    def personality_config(self):
        """Create test personality configuration."""
        from harmony.config.settings import PersonalityTrait
        return PersonalityConfig(
            name="TestBot",
            traits=[PersonalityTrait.HELPFUL, PersonalityTrait.CHEERFUL]
        )

    @pytest.fixture
    def mock_personality_manager(self, personality_config):
        """Create mock personality manager."""
        manager = Mock(spec=PersonalityManager)
        manager.get_system_prompt.return_value = "You are a helpful AI assistant."
        manager.config = personality_config
        return manager

    @pytest.fixture
    def mock_event_bus(self):
        """Create mock event bus."""
        bus = Mock(spec=EventBus)
        bus.emit = AsyncMock()
        bus.subscribe = Mock()
        return bus

    @pytest.fixture
    def ai_engine(self, ai_config, mock_personality_manager, mock_event_bus):
        """Create AIEngine instance for testing."""
        return AIEngine(ai_config, mock_personality_manager, mock_event_bus)

    def test_ai_engine_initialization(self, ai_engine, ai_config):
        """Test AIEngine initialization."""
        assert ai_engine.config == ai_config
        assert ai_engine.personality_manager is not None
        assert ai_engine.event_bus is not None
        assert ai_engine.client is None  # Not initialized yet
        assert ai_engine._running is False

    @pytest.mark.asyncio
    async def test_ai_engine_lifecycle(self, ai_engine):
        """Test AIEngine start/stop lifecycle."""
        # Mock the Gemini client
        with patch('google.genai.Client') as mock_client_class:
            mock_client = Mock()
            mock_client.aio = Mock()
            mock_client.aio.models = Mock()
            mock_client.aio.models.generate_content = AsyncMock(
                return_value=Mock(text="Connection test successful")
            )
            mock_client_class.return_value = mock_client

            # Initialize
            await ai_engine.initialize()
            assert ai_engine.client is not None

            # Start
            await ai_engine.start()
            assert ai_engine._running is True

            # Stop
            await ai_engine.stop()
            assert ai_engine._running is False

    def test_build_prompt(self, ai_engine):
        """Test prompt building functionality."""
        # Test basic prompt building
        prompt = ai_engine._build_prompt("Hello", "TestUser", "TestChannel")
        
        assert "You are a helpful AI assistant." in prompt
        assert "Hello" in prompt
        assert "TestUser" in prompt
        assert "TestChannel" in prompt

    def test_build_prompt_with_context(self, ai_engine):
        """Test prompt building with additional context."""
        context = {
            "previous_messages": ["How are you?", "I'm doing well"],
            "user_preferences": {"style": "formal"}
        }
        
        prompt = ai_engine._build_prompt(
            "What's the weather?", 
            "TestUser", 
            "TestChannel", 
            context
        )
        
        assert "You are a helpful AI assistant." in prompt
        assert "What's the weather?" in prompt
        assert "previous_messages" in prompt or "How are you?" in prompt

    @pytest.mark.asyncio
    async def test_generate_response_success(self, ai_engine):
        """Test successful response generation."""
        # Mock the Gemini client
        with patch('google.genai.Client') as mock_client_class:
            mock_response = Mock()
            mock_response.text = "Hello! How can I help you today?"
            
            mock_client = Mock()
            mock_client.aio = Mock()
            mock_client.aio.models = Mock()
            mock_client.aio.models.generate_content = AsyncMock(return_value=mock_response)
            mock_client_class.return_value = mock_client

            # Initialize the engine
            await ai_engine.initialize()
            await ai_engine.start()

            # Test response generation
            response = await ai_engine.generate_response(
                "Hello", "TestUser", "TestChannel"
            )

            assert response == "Hello! How can I help you today?"
            ai_engine.event_bus.emit.assert_called()

    @pytest.mark.asyncio
    async def test_generate_response_failure(self, ai_engine):
        """Test response generation failure handling."""
        # Mock the Gemini client to succeed on first call (initialization) but fail on second call
        with patch('google.genai.Client') as mock_client_class:
            mock_response = Mock()
            mock_response.text = "Connection test successful"

            mock_client = Mock()
            mock_client.aio = Mock()
            mock_client.aio.models = Mock()
            # First call succeeds (for initialization), second call fails
            mock_client.aio.models.generate_content = AsyncMock(
                side_effect=[mock_response, Exception("API Error")]
            )
            mock_client_class.return_value = mock_client

            # Initialize the engine
            await ai_engine.initialize()
            await ai_engine.start()

            # Test response generation failure
            response = await ai_engine.generate_response(
                "Hello", "TestUser", "TestChannel"
            )

            assert response is None

    @pytest.mark.asyncio
    async def test_test_connection_success(self, ai_engine):
        """Test successful connection testing."""
        with patch('google.genai.Client') as mock_client_class:
            mock_response = Mock()
            mock_response.text = "Connection test successful"
            
            mock_client = Mock()
            mock_client.aio = Mock()
            mock_client.aio.models = Mock()
            mock_client.aio.models.generate_content = AsyncMock(return_value=mock_response)
            mock_client_class.return_value = mock_client

            await ai_engine.initialize()
            result = await ai_engine.test_connection()
            
            assert result is True

    @pytest.mark.asyncio
    async def test_test_connection_failure(self, ai_engine):
        """Test connection testing failure."""
        with patch('google.genai.Client') as mock_client_class:
            mock_response = Mock()
            mock_response.text = "Connection test successful"

            mock_client = Mock()
            mock_client.aio = Mock()
            mock_client.aio.models = Mock()
            # First call succeeds (for initialization), second call fails
            mock_client.aio.models.generate_content = AsyncMock(
                side_effect=[mock_response, Exception("Connection failed")]
            )
            mock_client_class.return_value = mock_client

            await ai_engine.initialize()
            result = await ai_engine.test_connection()

            assert result is False

    def test_ai_engine_not_initialized(self, ai_engine):
        """Test behavior when engine is not initialized."""
        # Should handle gracefully when not initialized
        assert ai_engine.client is None
        assert ai_engine._running is False

    @pytest.mark.asyncio
    async def test_event_emission(self, ai_engine):
        """Test that events are properly emitted."""
        with patch('google.genai.Client') as mock_client_class:
            mock_response = Mock()
            mock_response.text = "Test response"
            
            mock_client = Mock()
            mock_client.aio = Mock()
            mock_client.aio.models = Mock()
            mock_client.aio.models.generate_content = AsyncMock(return_value=mock_response)
            mock_client_class.return_value = mock_client

            await ai_engine.initialize()
            await ai_engine.start()

            await ai_engine.generate_response("Hello", "TestUser", "TestChannel")

            # Verify events were emitted
            assert ai_engine.event_bus.emit.call_count >= 1
            
            # Check that AI response event was emitted
            calls = ai_engine.event_bus.emit.call_args_list
            event_types = [call[0][0].type for call in calls]
            assert EventType.AI_RESPONSE_GENERATED in event_types
