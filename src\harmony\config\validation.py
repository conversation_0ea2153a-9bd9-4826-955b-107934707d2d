"""
Configuration validation utilities for API connections and settings.
"""

import asyncio
import aiohttp
import ssl
from typing import Dict, Any, Optional, Tuple
from pathlib import Path

from ..utils.logger import get_logger

logger = get_logger(__name__)


class APIValidator:
    """Validates API connections and configurations."""
    
    @staticmethod
    async def validate_google_gemini(api_key: str) -> <PERSON><PERSON>[bool, str]:
        """
        Validate Google Gemini API key.
        
        Args:
            api_key: Google Gemini API key
            
        Returns:
            Tuple of (is_valid, message)
        """
        if not api_key or not api_key.startswith('AIza'):
            return False, "Invalid API key format. Google API keys should start with 'AIza'"
        
        try:
            # Test the API with a simple request
            import google.generativeai as genai
            
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel('gemini-2.0-flash-001')
            
            # Simple test prompt
            response = await asyncio.to_thread(
                model.generate_content, 
                "Say 'API test successful' if you can read this."
            )
            
            if response and response.text:
                return True, "Google Gemini API connection successful"
            else:
                return False, "Google Gemini API returned empty response"
                
        except Exception as e:
            return False, f"Google Gemini API validation failed: {str(e)}"
    
    @staticmethod
    async def validate_elevenlabs(api_key: str) -> Tuple[bool, str]:
        """
        Validate ElevenLabs API key.
        
        Args:
            api_key: ElevenLabs API key
            
        Returns:
            Tuple of (is_valid, message)
        """
        if not api_key or not api_key.startswith('sk_'):
            return False, "Invalid API key format. ElevenLabs API keys should start with 'sk_'"
        
        try:
            headers = {
                'xi-api-key': api_key,
                'Content-Type': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    'https://api.elevenlabs.io/v1/user',
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        user_data = await response.json()
                        return True, f"ElevenLabs API connection successful. User: {user_data.get('email', 'Unknown')}"
                    elif response.status == 401:
                        return False, "ElevenLabs API key is invalid or expired"
                    else:
                        return False, f"ElevenLabs API returned status {response.status}"
                        
        except asyncio.TimeoutError:
            return False, "ElevenLabs API connection timeout"
        except Exception as e:
            return False, f"ElevenLabs API validation failed: {str(e)}"
    
    @staticmethod
    async def validate_twitch_credentials(client_id: str, client_secret: str) -> Tuple[bool, str]:
        """
        Validate Twitch API credentials.
        
        Args:
            client_id: Twitch client ID
            client_secret: Twitch client secret
            
        Returns:
            Tuple of (is_valid, message)
        """
        if not client_id or not client_secret:
            return False, "Twitch client ID and secret are required"
        
        try:
            # Get app access token to validate credentials
            data = {
                'client_id': client_id,
                'client_secret': client_secret,
                'grant_type': 'client_credentials'
            }

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'https://id.twitch.tv/oauth2/token',
                    data=data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        token_data = await response.json()
                        access_token = token_data.get('access_token')
                        
                        # Validate the token by making a test API call
                        headers = {
                            'Client-ID': client_id,
                            'Authorization': f'Bearer {access_token}'
                        }

                        # Test with a simple API call that doesn't require user parameters
                        async with session.get(
                            'https://api.twitch.tv/helix/games/top?first=1',
                            headers=headers,
                            timeout=aiohttp.ClientTimeout(total=10)
                        ) as validate_response:
                            if validate_response.status == 200:
                                return True, "Twitch API credentials are valid"
                            elif validate_response.status == 400:
                                # Check if it's the expected "missing user parameter" error
                                try:
                                    error_data = await validate_response.json()
                                    if "id or login query parameter is required" in error_data.get('message', ''):
                                        return True, "Twitch API credentials are valid (app access token working)"
                                except:
                                    pass
                                return False, f"Twitch API validation failed with status {validate_response.status}"
                            else:
                                return False, f"Twitch API validation failed with status {validate_response.status}"
                    else:
                        try:
                            error_data = await response.json()
                            error_msg = error_data.get('message', 'Unknown error')
                            return False, f"Twitch OAuth failed (status {response.status}): {error_msg}"
                        except:
                            error_text = await response.text()
                            return False, f"Twitch OAuth failed (status {response.status}): {error_text[:200]}"
                        
        except asyncio.TimeoutError:
            return False, "Twitch API connection timeout"
        except Exception as e:
            return False, f"Twitch API validation failed: {str(e)}"
    
    @staticmethod
    async def validate_ollama_connection(base_url: str, model: str) -> Tuple[bool, str]:
        """
        Validate Ollama local model connection.
        
        Args:
            base_url: Ollama base URL
            model: Model name to test
            
        Returns:
            Tuple of (is_valid, message)
        """
        try:
            async with aiohttp.ClientSession() as session:
                # Check if Ollama is running
                async with session.get(
                    f"{base_url}/api/tags",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        models_data = await response.json()
                        available_models = [m['name'] for m in models_data.get('models', [])]
                        
                        if model in available_models:
                            return True, f"Ollama connection successful. Model '{model}' is available"
                        else:
                            return False, f"Model '{model}' not found. Available models: {', '.join(available_models)}"
                    else:
                        return False, f"Ollama API returned status {response.status}"
                        
        except aiohttp.ClientConnectorError:
            return False, f"Cannot connect to Ollama at {base_url}. Is Ollama running?"
        except asyncio.TimeoutError:
            return False, "Ollama connection timeout"
        except Exception as e:
            return False, f"Ollama validation failed: {str(e)}"


class ConfigValidator:
    """Validates configuration settings and file structure."""
    
    @staticmethod
    def validate_file_paths(config_data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validate that required file paths exist and are accessible.
        
        Args:
            config_data: Configuration dictionary
            
        Returns:
            Tuple of (is_valid, message)
        """
        issues = []
        
        # Check database directory
        db_url = config_data.get('database', {}).get('url', '')
        if db_url.startswith('sqlite:///'):
            db_path = Path(db_url.replace('sqlite:///', ''))
            db_dir = db_path.parent
            if not db_dir.exists():
                try:
                    db_dir.mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    issues.append(f"Cannot create database directory {db_dir}: {e}")
        
        # Check log directory
        log_path = config_data.get('logging', {}).get('file_path', '')
        if log_path:
            log_dir = Path(log_path).parent
            if not log_dir.exists():
                try:
                    log_dir.mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    issues.append(f"Cannot create log directory {log_dir}: {e}")
        
        # Check audio cache directory
        audio_cache = config_data.get('audio', {}).get('cache_dir', 'data/audio')
        audio_dir = Path(audio_cache)
        if not audio_dir.exists():
            try:
                audio_dir.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                issues.append(f"Cannot create audio cache directory {audio_dir}: {e}")
        
        if issues:
            return False, "; ".join(issues)
        else:
            return True, "All file paths are valid and accessible"
    
    @staticmethod
    def validate_security_settings(config_data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validate security configuration settings.
        
        Args:
            config_data: Configuration dictionary
            
        Returns:
            Tuple of (is_valid, message)
        """
        issues = []
        security = config_data.get('security', {})
        
        # Check secret key
        secret_key = security.get('secret_key', '')
        if not secret_key:
            issues.append("Secret key is required for encryption")
        elif len(secret_key) < 32:
            issues.append("Secret key should be at least 32 characters long")
        
        # Check token encryption password
        token_password = security.get('token_encryption_password', '')
        if not token_password:
            issues.append("Token encryption password is required")
        elif len(token_password) < 8:
            issues.append("Token encryption password should be at least 8 characters long")
        
        if issues:
            return False, "; ".join(issues)
        else:
            return True, "Security settings are valid"


async def validate_all_apis(config_data: Dict[str, Any]) -> Dict[str, Tuple[bool, str]]:
    """
    Validate all API connections concurrently.
    
    Args:
        config_data: Configuration dictionary
        
    Returns:
        Dictionary mapping API names to (is_valid, message) tuples
    """
    results = {}
    
    # Extract API configurations
    ai_config = config_data.get('ai', {})
    twitch_config = config_data.get('twitch', {})
    
    # Create validation tasks
    tasks = []
    
    # Google Gemini
    if ai_config.get('gemini_api_key'):
        tasks.append(('gemini', APIValidator.validate_google_gemini(ai_config['gemini_api_key'])))
    
    # ElevenLabs
    if ai_config.get('elevenlabs_api_key'):
        tasks.append(('elevenlabs', APIValidator.validate_elevenlabs(ai_config['elevenlabs_api_key'])))
    
    # Twitch
    if twitch_config.get('client_id') and twitch_config.get('client_secret'):
        tasks.append(('twitch', APIValidator.validate_twitch_credentials(
            twitch_config['client_id'], 
            twitch_config['client_secret']
        )))
    
    # Ollama
    if ai_config.get('ollama_base_url') and ai_config.get('ollama_model'):
        tasks.append(('ollama', APIValidator.validate_ollama_connection(
            ai_config['ollama_base_url'], 
            ai_config['ollama_model']
        )))
    
    # Run all validations concurrently
    if tasks:
        task_names, task_coroutines = zip(*tasks)
        task_results = await asyncio.gather(*task_coroutines, return_exceptions=True)
        
        for name, result in zip(task_names, task_results):
            if isinstance(result, Exception):
                results[name] = (False, f"Validation error: {str(result)}")
            else:
                results[name] = result
    
    return results
