@echo off
REM Windows setup script for Harmony AI Twitch Co-Host Bot
echo 🚀 Harmony AI Twitch Co-Host Bot - Windows Setup
echo ================================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.9+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python detected

REM Create virtual environment
if not exist ".venv" (
    echo 🔄 Creating virtual environment...
    python -m venv .venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created
) else (
    echo ✅ Virtual environment already exists
)

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call .venv\Scripts\activate.bat

REM Upgrade pip
echo 🔄 Upgrading pip...
python -m pip install --upgrade pip

REM Install core dependencies
echo 🔄 Installing core dependencies...
python -m pip install -r requirements-core.txt
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    echo Trying alternative installation...
    python -m pip install pydantic python-dotenv pyyaml click requests websockets sqlalchemy cryptography rich colorlog watchdog google-genai elevenlabs twitchio
)

REM Create directories
echo 🔄 Creating directories...
if not exist "data" mkdir data
if not exist "data\logs" mkdir data\logs
if not exist "data\audio" mkdir data\audio
if not exist "cache" mkdir cache
if not exist "config" mkdir config
echo ✅ Directories created

REM Setup environment file
if exist ".env" (
    echo ⚠️  .env file already exists
    set /p overwrite="Overwrite existing .env file? (y/N): "
    if /i not "%overwrite%"=="y" (
        echo ✅ Keeping existing .env file
        goto :verify
    )
)

echo.
echo ================================================
echo 🔑 API KEY CONFIGURATION
echo ================================================
echo Please enter your API keys below.
echo You can find these keys at:
echo • Google Gemini: https://ai.google.dev/
echo • ElevenLabs: https://elevenlabs.io/
echo • Twitch: https://dev.twitch.tv/console/apps
echo.
echo Note: Your input will be visible on screen.
echo ------------------------------------------------

set /p GOOGLE_API_KEY="Google Gemini API Key: "
set /p ELEVENLABS_API_KEY="ElevenLabs API Key: "
set /p TWITCH_CLIENT_ID="Twitch Client ID: "
set /p TWITCH_CLIENT_SECRET="Twitch Client Secret: "
echo.
echo Optional API keys (press Enter to skip):
set /p PERPLEXITY_API_KEY="Perplexity API Key (optional): "
echo.
set /p TOKEN_PASSWORD="Token Encryption Password (choose a strong password): "

REM Generate secret key (simplified)
set SECRET_KEY=harmony_secret_key_%RANDOM%%RANDOM%%RANDOM%%RANDOM%

REM Create .env file
echo # Google Gemini API Configuration > .env
echo GOOGLE_API_KEY=%GOOGLE_API_KEY% >> .env
echo. >> .env
echo # ElevenLabs TTS Configuration >> .env
echo ELEVENLABS_API_KEY=%ELEVENLABS_API_KEY% >> .env
echo. >> .env
echo # Twitch Application Configuration >> .env
echo TWITCH_CLIENT_ID=%TWITCH_CLIENT_ID% >> .env
echo TWITCH_CLIENT_SECRET=%TWITCH_CLIENT_SECRET% >> .env
echo TWITCH_REDIRECT_URI=http://localhost:8080/auth/callback >> .env
echo. >> .env
echo # Optional: Perplexity API for Research >> .env
echo PERPLEXITY_API_KEY=%PERPLEXITY_API_KEY% >> .env
echo. >> .env
echo # Optional: Ollama Configuration (for local models) >> .env
echo OLLAMA_BASE_URL=http://localhost:11434 >> .env
echo OLLAMA_MODEL=llama2 >> .env
echo. >> .env
echo # Database Configuration >> .env
echo DATABASE_URL=sqlite:///data/harmony.db >> .env
echo. >> .env
echo # Security Configuration >> .env
echo SECRET_KEY=%SECRET_KEY% >> .env
echo TOKEN_ENCRYPTION_PASSWORD=%TOKEN_PASSWORD% >> .env
echo. >> .env
echo # Logging Configuration >> .env
echo LOG_LEVEL=INFO >> .env
echo LOG_FILE=data/logs/harmony.log >> .env
echo. >> .env
echo # Audio Configuration >> .env
echo AUDIO_INPUT_DEVICE=default >> .env
echo AUDIO_OUTPUT_DEVICE=default >> .env
echo AUDIO_SAMPLE_RATE=44100 >> .env
echo. >> .env
echo # Feature Toggles >> .env
echo ENABLE_TTS=true >> .env
echo ENABLE_STT=false >> .env
echo ENABLE_TRIVIA=true >> .env
echo ENABLE_MODERATION=true >> .env
echo ENABLE_GOAL_TRACKING=true >> .env
echo ENABLE_REMINDERS=true >> .env
echo. >> .env
echo # Development Settings >> .env
echo DEBUG=false >> .env
echo DEVELOPMENT_MODE=false >> .env
echo MOCK_TWITCH_API=false >> .env

echo ✅ Created .env file with your configuration

:verify
REM Verify installation
echo 🔄 Verifying installation...
python -c "import google.genai; print('✅ Google Gemini')" 2>nul || echo "❌ Google Gemini"
python -c "import elevenlabs; print('✅ ElevenLabs')" 2>nul || echo "❌ ElevenLabs"
python -c "import twitchio; print('✅ TwitchIO')" 2>nul || echo "❌ TwitchIO"
python -c "import pydantic; print('✅ Pydantic')" 2>nul || echo "❌ Pydantic"

echo.
echo ================================================
echo 🎉 SETUP COMPLETED!
echo ================================================
echo.
echo Next steps:
echo 1. The virtual environment is already activated
echo 2. Test the installation:
echo    python run.py --help
echo 3. Start the bot:
echo    python run.py
echo.
echo For future use:
echo • Run activate.bat to activate the virtual environment
echo • Use python run.py --gui for GUI mode
echo • Use python run.py --debug for debug mode
echo.
pause
