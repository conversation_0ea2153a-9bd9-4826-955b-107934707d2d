#!/usr/bin/env python3
"""
Unit tests for Twitch Client functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock

from harmony.twitch.client import TwitchClient
from harmony.config.settings import TwitchConfig
from harmony.core.events import EventBus


class TestTwitchClient:
    """Test cases for TwitchClient class."""

    @pytest.fixture
    def twitch_config(self):
        """Create test Twitch configuration."""
        return TwitchConfig(
            client_id="test_client_id",
            client_secret="test_client_secret",
            bot_token="test_bot_token",
            streamer_token="test_streamer_token",
            bot_username="test_bot",
            streamer_username="test_streamer"
        )

    @pytest.fixture
    def mock_event_bus(self):
        """Create mock event bus."""
        bus = Mock(spec=EventBus)
        bus.emit = AsyncMock()
        bus.subscribe = Mock()
        return bus

    @pytest.fixture
    def twitch_client(self, twitch_config, mock_event_bus):
        """Create TwitchClient instance for testing."""
        return TwitchClient(twitch_config, mock_event_bus)

    def test_twitch_client_initialization(self, twitch_client, twitch_config):
        """Test TwitchClient initialization."""
        assert twitch_client.config == twitch_config
        assert twitch_client.event_bus is not None
        assert twitch_client.bot is None  # Not initialized yet
        assert twitch_client._running is False

    @pytest.mark.asyncio
    async def test_twitch_client_lifecycle(self, twitch_client):
        """Test TwitchClient start/stop lifecycle."""
        # Mock the TwitchIO bot
        with patch('twitchio.ext.commands.Bot') as mock_bot_class:
            mock_bot = Mock()
            mock_bot.start = AsyncMock()
            mock_bot.close = AsyncMock()
            mock_bot_class.return_value = mock_bot

            # Initialize
            await twitch_client.initialize()
            assert twitch_client.bot is not None

            # Start
            await twitch_client.start()
            assert twitch_client._running is True
            mock_bot.start.assert_called_once()

            # Stop
            await twitch_client.stop()
            assert twitch_client._running is False
            mock_bot.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_send_message_success(self, twitch_client):
        """Test successful message sending."""
        with patch('twitchio.ext.commands.Bot') as mock_bot_class:
            # Mock channel and send method
            mock_channel = Mock()
            mock_channel.send = AsyncMock()

            mock_bot = Mock()
            mock_bot.get_channel.return_value = mock_channel
            mock_bot_class.return_value = mock_bot

            await twitch_client.initialize()
            await twitch_client.start()

            # Test message sending
            result = await twitch_client.send_message("test_streamer", "Hello, chat!")

            assert result is True
            mock_channel.send.assert_called_once_with("Hello, chat!")

    @pytest.mark.asyncio
    async def test_send_message_failure(self, twitch_client):
        """Test message sending failure handling."""
        with patch('twitchio.ext.commands.Bot') as mock_bot_class:
            # Mock channel to raise exception
            mock_channel = Mock()
            mock_channel.send = AsyncMock(side_effect=Exception("Send failed"))

            mock_bot = Mock()
            mock_bot.get_channel.return_value = mock_channel
            mock_bot_class.return_value = mock_bot

            await twitch_client.initialize()
            await twitch_client.start()

            # Test message sending failure
            result = await twitch_client.send_message("test_streamer", "Hello, chat!")

            assert result is False

    @pytest.mark.asyncio
    async def test_test_connection_success(self, twitch_client):
        """Test successful connection testing."""
        with patch('twitchio.Bot') as mock_bot_class:
            mock_bot = Mock()
            mock_bot.start = AsyncMock()
            mock_bot.close = AsyncMock()
            mock_bot_class.return_value = mock_bot

            await twitch_client.initialize()
            result = await twitch_client.test_connection()
            
            assert result is True

    @pytest.mark.asyncio
    async def test_test_connection_failure(self, twitch_client):
        """Test connection testing failure."""
        with patch('twitchio.Bot') as mock_bot_class:
            mock_bot = Mock()
            mock_bot.start = AsyncMock(side_effect=Exception("Connection failed"))
            mock_bot_class.return_value = mock_bot

            await twitch_client.initialize()
            result = await twitch_client.test_connection()
            
            assert result is False

    def test_message_parsing(self, twitch_client):
        """Test message parsing functionality."""
        # Mock message object
        mock_message = Mock()
        mock_message.content = "!help test command"
        mock_message.author = Mock()
        mock_message.author.name = "test_user"
        mock_message.channel = Mock()
        mock_message.channel.name = "test_channel"

        # Test command detection
        is_command = twitch_client._is_command(mock_message.content)
        assert is_command is True

        # Test command parsing
        command, args = twitch_client._parse_command(mock_message.content)
        assert command == "help"
        assert args == ["test", "command"]

    def test_non_command_message(self, twitch_client):
        """Test handling of non-command messages."""
        # Test regular message
        is_command = twitch_client._is_command("Hello everyone!")
        assert is_command is False

        # Test empty message
        is_command = twitch_client._is_command("")
        assert is_command is False

    @pytest.mark.asyncio
    async def test_message_event_handling(self, twitch_client):
        """Test message event handling."""
        with patch('twitchio.Bot') as mock_bot_class:
            mock_bot = Mock()
            mock_bot_class.return_value = mock_bot

            await twitch_client.initialize()

            # Mock message
            mock_message = Mock()
            mock_message.content = "Hello, bot!"
            mock_message.author = Mock()
            mock_message.author.name = "test_user"
            mock_message.channel = Mock()
            mock_message.channel.name = "test_channel"

            # Simulate message event
            await twitch_client._handle_message(mock_message)

            # Verify event was emitted
            twitch_client.event_bus.emit.assert_called()

    @pytest.mark.asyncio
    async def test_command_event_handling(self, twitch_client):
        """Test command event handling."""
        with patch('twitchio.Bot') as mock_bot_class:
            mock_bot = Mock()
            mock_bot_class.return_value = mock_bot

            await twitch_client.initialize()

            # Mock command message
            mock_message = Mock()
            mock_message.content = "!help"
            mock_message.author = Mock()
            mock_message.author.name = "test_user"
            mock_message.channel = Mock()
            mock_message.channel.name = "test_channel"

            # Simulate command event
            await twitch_client._handle_message(mock_message)

            # Verify command event was emitted
            twitch_client.event_bus.emit.assert_called()
            
            # Check event type
            calls = twitch_client.event_bus.emit.call_args_list
            event_types = [call[0][0].type for call in calls]
            assert "twitch_command" in event_types

    def test_channel_configuration(self, twitch_client):
        """Test channel configuration."""
        assert twitch_client.config.channel == "test_channel"
        assert twitch_client.config.bot_username == "test_bot"
        assert twitch_client.config.command_prefix == "!"

    @pytest.mark.asyncio
    async def test_bot_not_initialized(self, twitch_client):
        """Test behavior when bot is not initialized."""
        # Should handle gracefully when not initialized
        result = await twitch_client.send_message("Test")
        assert result is False

    @pytest.mark.asyncio
    async def test_channel_not_found(self, twitch_client):
        """Test behavior when channel is not found."""
        with patch('twitchio.Bot') as mock_bot_class:
            mock_bot = Mock()
            mock_bot.get_channel.return_value = None  # Channel not found
            mock_bot_class.return_value = mock_bot

            await twitch_client.initialize()
            await twitch_client.start()

            result = await twitch_client.send_message("Test")
            assert result is False

    def test_command_prefix_validation(self, twitch_client):
        """Test command prefix validation."""
        # Test with correct prefix
        assert twitch_client._is_command("!help") is True
        assert twitch_client._is_command("!test command") is True
        
        # Test without prefix
        assert twitch_client._is_command("help") is False
        assert twitch_client._is_command("test command") is False
        
        # Test with wrong prefix
        assert twitch_client._is_command("?help") is False
        assert twitch_client._is_command("#help") is False
