@echo off
REM Activation script for Windows
echo 🚀 Activating Harmony Bot virtual environment...

if not exist ".venv" (
    echo ❌ Virtual environment not found. Please run setup.py first.
    pause
    exit /b 1
)

call .venv\Scripts\activate.bat
echo ✅ Virtual environment activated!
echo.
echo Available commands:
echo   python run.py --help     - Show help
echo   python run.py            - Start the bot
echo   python run.py --debug    - Start in debug mode
echo   python run.py --gui      - Start with GUI
echo.
cmd /k
