#!/usr/bin/env python3
"""
Simplified unit tests for AI Engine focusing on internal logic.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch

from harmony.ai.llm_client import AIEngine
from harmony.config.settings import AIConfig, PersonalityConfig, PersonalityTrait
from harmony.core.personality import PersonalityManager
from harmony.core.events import EventBus


class TestAIEngineSimple:
    """Simplified test cases for AIEngine class."""

    @pytest.fixture
    def ai_config(self):
        """Create test AI configuration."""
        return AIConfig(
            gemini_api_key="test_gemini_key",
            gemini_model="gemini-pro",
            elevenlabs_api_key="test_elevenlabs_key"
        )

    @pytest.fixture
    def personality_config(self):
        """Create test personality configuration."""
        return PersonalityConfig(
            name="TestBot",
            traits=[PersonalityTrait.HELPFUL, PersonalityTrait.CHEERFUL]
        )

    @pytest.fixture
    def mock_personality_manager(self, personality_config):
        """Create mock personality manager."""
        manager = Mock(spec=PersonalityManager)
        manager.get_system_prompt.return_value = "You are a helpful AI assistant."
        manager.config = personality_config
        return manager

    @pytest.fixture
    def mock_event_bus(self):
        """Create mock event bus."""
        bus = Mock(spec=EventBus)
        bus.emit = AsyncMock()
        bus.subscribe = Mock()
        return bus

    @pytest.fixture
    def ai_engine(self, ai_config, mock_personality_manager, mock_event_bus):
        """Create AIEngine instance for testing."""
        return AIEngine(ai_config, mock_personality_manager, mock_event_bus)

    def test_ai_engine_initialization(self, ai_engine, ai_config):
        """Test AIEngine initialization."""
        assert ai_engine.config == ai_config
        assert ai_engine.personality_manager is not None
        assert ai_engine.event_bus is not None
        assert ai_engine.client is None  # Not initialized yet
        assert ai_engine._running is False

    def test_build_prompt_basic(self, ai_engine):
        """Test basic prompt building functionality."""
        prompt = ai_engine._build_prompt("Hello", "TestUser", "TestChannel")
        
        assert isinstance(prompt, str)
        assert len(prompt) > 0
        assert "You are a helpful AI assistant." in prompt
        assert "Hello" in prompt
        assert "TestUser" in prompt
        # Channel name might not be in prompt, but other elements should be
        assert "Hello" in prompt or "TestUser" in prompt

    def test_build_prompt_with_context(self, ai_engine):
        """Test prompt building with additional context."""
        context = {
            "previous_messages": ["How are you?", "I'm doing well"],
            "user_preferences": {"style": "formal"}
        }
        
        prompt = ai_engine._build_prompt(
            "What's the weather?", 
            "TestUser", 
            "TestChannel", 
            context
        )
        
        assert isinstance(prompt, str)
        assert len(prompt) > 0
        assert "You are a helpful AI assistant." in prompt
        assert "What's the weather?" in prompt

    def test_build_prompt_empty_message(self, ai_engine):
        """Test prompt building with empty message."""
        prompt = ai_engine._build_prompt("", "TestUser", "TestChannel")
        
        assert isinstance(prompt, str)
        assert len(prompt) > 0
        assert "You are a helpful AI assistant." in prompt

    def test_build_prompt_none_values(self, ai_engine):
        """Test prompt building with None values."""
        prompt = ai_engine._build_prompt("Hello", None, None)
        
        assert isinstance(prompt, str)
        assert len(prompt) > 0
        assert "Hello" in prompt

    def test_configuration_access(self, ai_engine):
        """Test configuration access."""
        assert ai_engine.config.gemini_api_key == "test_gemini_key"
        assert ai_engine.config.gemini_model == "gemini-pro"
        assert ai_engine.config.elevenlabs_api_key == "test_elevenlabs_key"

    def test_personality_manager_integration(self, ai_engine):
        """Test personality manager integration."""
        # Test that personality manager is called
        prompt = ai_engine._build_prompt("Test", "User", "Channel")
        ai_engine.personality_manager.get_system_prompt.assert_called()
        assert isinstance(prompt, str)

    def test_event_bus_integration(self, ai_engine):
        """Test event bus integration."""
        assert ai_engine.event_bus is not None
        assert hasattr(ai_engine.event_bus, 'emit')
        assert hasattr(ai_engine.event_bus, 'subscribe')

    def test_state_properties(self, ai_engine):
        """Test state tracking properties."""
        assert ai_engine._running is False
        assert ai_engine.client is None

    def test_prompt_building_with_different_personalities(self):
        """Test prompt building with different personality configurations."""
        configs = [
            PersonalityConfig(name="Bot1", traits=[PersonalityTrait.PROFESSIONAL]),
            PersonalityConfig(name="Bot2", traits=[PersonalityTrait.HUMOROUS]),
            PersonalityConfig(name="Bot3", traits=[PersonalityTrait.ANALYTICAL])
        ]
        
        for config in configs:
            mock_pm = Mock(spec=PersonalityManager)
            mock_pm.get_system_prompt.return_value = f"You are {config.name}."
            mock_pm.config = config
            
            mock_eb = Mock(spec=EventBus)
            ai_config = AIConfig(gemini_api_key="test", elevenlabs_api_key="test")
            
            engine = AIEngine(ai_config, mock_pm, mock_eb)
            prompt = engine._build_prompt("Hello", "User", "Channel")
            
            assert isinstance(prompt, str)
            assert config.name in prompt

    @pytest.mark.asyncio
    async def test_generate_response_not_initialized(self, ai_engine):
        """Test response generation when not initialized."""
        # Should handle gracefully when not initialized
        response = await ai_engine.generate_response("Hello", "User", "Channel")
        assert response is None

    def test_ai_engine_attributes(self, ai_engine):
        """Test that all expected attributes exist."""
        expected_attrs = [
            'config', 'personality_manager', 'event_bus', 'client', '_running'
        ]
        
        for attr in expected_attrs:
            assert hasattr(ai_engine, attr), f"Missing attribute: {attr}"

    def test_prompt_context_handling(self, ai_engine):
        """Test different context scenarios."""
        # Test with empty context
        prompt1 = ai_engine._build_prompt("Hello", "User", "Channel", {})
        assert isinstance(prompt1, str)
        
        # Test with None context
        prompt2 = ai_engine._build_prompt("Hello", "User", "Channel", None)
        assert isinstance(prompt2, str)
        
        # Test with complex context
        complex_context = {
            "history": ["msg1", "msg2"],
            "settings": {"tone": "casual"},
            "metadata": {"timestamp": "2024-01-01"}
        }
        prompt3 = ai_engine._build_prompt("Hello", "User", "Channel", complex_context)
        assert isinstance(prompt3, str)

    def test_personality_prompt_integration(self, ai_engine):
        """Test that personality prompts are properly integrated."""
        # Change the personality manager response
        ai_engine.personality_manager.get_system_prompt.return_value = "Custom personality prompt."
        
        prompt = ai_engine._build_prompt("Test message", "TestUser", "TestChannel")
        
        assert "Custom personality prompt." in prompt
        assert "Test message" in prompt
