#!/usr/bin/env python3
"""
Unit tests for Configuration Validation.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

from harmony.config.validation import ConfigValidator, validate_all_apis


class TestConfigValidator:
    """Test cases for ConfigValidator class."""

    def test_validate_file_paths_valid(self):
        """Test file path validation with valid paths."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = {
                "logging": {
                    "log_file": str(Path(temp_dir) / "test.log")
                },
                "audio": {
                    "output_directory": temp_dir
                }
            }
            
            is_valid, message = ConfigValidator.validate_file_paths(config)
            assert is_valid is True
            assert "valid" in message.lower()

    def test_validate_file_paths_invalid_directory(self):
        """Test file path validation with invalid directory."""
        config = {
            "audio": {
                "output_directory": "Z:\\definitely\\nonexistent\\invalid\\path"
            }
        }

        is_valid, message = ConfigValidator.validate_file_paths(config)
        assert is_valid is False
        assert "directory" in message.lower()

    def test_validate_file_paths_no_paths(self):
        """Test file path validation with no paths configured."""
        config = {
            "twitch": {
                "client_id": "test"
            }
        }
        
        is_valid, message = ConfigValidator.validate_file_paths(config)
        assert is_valid is True

    def test_validate_security_settings_valid(self):
        """Test security settings validation with valid settings."""
        config = {
            "security": {
                "secret_key": "a" * 32,  # 32 character key
                "token_encryption_password": "secure_password_123"
            }
        }
        
        is_valid, message = ConfigValidator.validate_security_settings(config)
        assert is_valid is True
        assert "valid" in message.lower()

    def test_validate_security_settings_short_key(self):
        """Test security settings validation with short secret key."""
        config = {
            "security": {
                "secret_key": "short",
                "token_encryption_password": "secure_password_123"
            }
        }
        
        is_valid, message = ConfigValidator.validate_security_settings(config)
        assert is_valid is False
        assert "secret_key" in message.lower()

    def test_validate_security_settings_weak_password(self):
        """Test security settings validation with weak password."""
        config = {
            "security": {
                "secret_key": "a" * 32,
                "token_encryption_password": "123"  # Too short
            }
        }
        
        is_valid, message = ConfigValidator.validate_security_settings(config)
        assert is_valid is False
        assert "password" in message.lower()

    def test_validate_security_settings_missing(self):
        """Test security settings validation with missing settings."""
        config = {}
        
        is_valid, message = ConfigValidator.validate_security_settings(config)
        assert is_valid is False
        assert "missing" in message.lower()

    def test_validate_api_keys_valid(self):
        """Test API key validation with valid keys."""
        config = {
            "ai": {
                "gemini_api_key": "valid_gemini_key_123",
                "elevenlabs_api_key": "valid_elevenlabs_key_456"
            },
            "twitch": {
                "client_id": "twitch_client_id",
                "client_secret": "twitch_client_secret"
            }
        }
        
        is_valid, message = ConfigValidator.validate_api_keys(config)
        assert is_valid is True
        assert "valid" in message.lower()

    def test_validate_api_keys_missing_gemini(self):
        """Test API key validation with missing Gemini key."""
        config = {
            "ai": {
                "elevenlabs_api_key": "valid_elevenlabs_key_456"
            }
        }
        
        is_valid, message = ConfigValidator.validate_api_keys(config)
        assert is_valid is False
        assert "gemini" in message.lower()

    def test_validate_api_keys_missing_elevenlabs(self):
        """Test API key validation with missing ElevenLabs key."""
        config = {
            "ai": {
                "gemini_api_key": "valid_gemini_key_123"
            }
        }
        
        is_valid, message = ConfigValidator.validate_api_keys(config)
        assert is_valid is False
        assert "elevenlabs" in message.lower()

    def test_validate_api_keys_empty_keys(self):
        """Test API key validation with empty keys."""
        config = {
            "ai": {
                "gemini_api_key": "",
                "elevenlabs_api_key": ""
            }
        }
        
        is_valid, message = ConfigValidator.validate_api_keys(config)
        assert is_valid is False

    def test_validate_twitch_config_valid(self):
        """Test Twitch configuration validation with valid config."""
        config = {
            "twitch": {
                "client_id": "valid_client_id",
                "client_secret": "valid_client_secret",
                "bot_username": "test_bot",
                "streamer_username": "test_streamer"
            }
        }
        
        is_valid, message = ConfigValidator.validate_twitch_config(config)
        assert is_valid is True
        assert "valid" in message.lower()

    def test_validate_twitch_config_missing_client_id(self):
        """Test Twitch configuration validation with missing client ID."""
        config = {
            "twitch": {
                "client_secret": "valid_client_secret"
            }
        }
        
        is_valid, message = ConfigValidator.validate_twitch_config(config)
        assert is_valid is False
        assert "client_id" in message.lower()

    def test_validate_twitch_config_missing_section(self):
        """Test Twitch configuration validation with missing section."""
        config = {}
        
        is_valid, message = ConfigValidator.validate_twitch_config(config)
        assert is_valid is False
        assert "twitch" in message.lower()

    def test_validate_audio_config_valid(self):
        """Test audio configuration validation with valid config."""
        config = {
            "audio": {
                "enabled": True,
                "volume": 0.8,
                "voice_name": "Rachel"
            }
        }
        
        is_valid, message = ConfigValidator.validate_audio_config(config)
        assert is_valid is True
        assert "valid" in message.lower()

    def test_validate_audio_config_invalid_volume(self):
        """Test audio configuration validation with invalid volume."""
        config = {
            "audio": {
                "enabled": True,
                "volume": 1.5  # Invalid: > 1.0
            }
        }
        
        is_valid, message = ConfigValidator.validate_audio_config(config)
        assert is_valid is False
        assert "volume" in message.lower()

    def test_validate_audio_config_disabled(self):
        """Test audio configuration validation when disabled."""
        config = {
            "audio": {
                "enabled": False
            }
        }
        
        is_valid, message = ConfigValidator.validate_audio_config(config)
        assert is_valid is True  # Should be valid even when disabled

    @pytest.mark.asyncio
    async def test_validate_all_apis_success(self):
        """Test validate_all_apis with successful validations."""
        config = {
            "ai": {
                "gemini_api_key": "test_key",
                "elevenlabs_api_key": "test_key"
            },
            "twitch": {
                "client_id": "test_id",
                "client_secret": "test_secret"
            }
        }
        
        # Mock the individual validation functions
        with patch('harmony.config.validation.APIValidator.validate_google_gemini') as mock_gemini, \
             patch('harmony.config.validation.APIValidator.validate_elevenlabs') as mock_elevenlabs, \
             patch('harmony.config.validation.APIValidator.validate_twitch_credentials') as mock_twitch:

            mock_gemini.return_value = (True, "Gemini API valid")
            mock_elevenlabs.return_value = (True, "ElevenLabs API valid")
            mock_twitch.return_value = (True, "Twitch API valid")
            
            results = await validate_all_apis(config)
            
            assert isinstance(results, dict)
            assert "gemini" in results
            assert "elevenlabs" in results
            assert "twitch" in results

    @pytest.mark.asyncio
    async def test_validate_all_apis_failures(self):
        """Test validate_all_apis with some failures."""
        config = {
            "ai": {
                "gemini_api_key": "invalid_key",
                "elevenlabs_api_key": "invalid_key"
            }
        }
        
        # Mock the individual validation functions
        with patch('harmony.config.validation.APIValidator.validate_google_gemini') as mock_gemini, \
             patch('harmony.config.validation.APIValidator.validate_elevenlabs') as mock_elevenlabs:

            mock_gemini.return_value = (False, "Invalid Gemini API key")
            mock_elevenlabs.return_value = (False, "Invalid ElevenLabs API key")
            
            results = await validate_all_apis(config)
            
            assert isinstance(results, dict)
            assert results["gemini"][0] is False
            assert results["elevenlabs"][0] is False

    def test_config_validator_methods_exist(self):
        """Test that all expected ConfigValidator methods exist."""
        expected_methods = [
            'validate_file_paths',
            'validate_security_settings', 
            'validate_api_keys',
            'validate_twitch_config',
            'validate_audio_config'
        ]
        
        for method in expected_methods:
            assert hasattr(ConfigValidator, method)
            assert callable(getattr(ConfigValidator, method))

    def test_validate_file_paths_edge_cases(self):
        """Test file path validation edge cases."""
        # Test with None values
        config = {
            "logging": {
                "log_file": None
            }
        }
        
        is_valid, message = ConfigValidator.validate_file_paths(config)
        assert isinstance(is_valid, bool)
        assert isinstance(message, str)

    def test_validate_security_settings_edge_cases(self):
        """Test security settings validation edge cases."""
        # Test with None values
        config = {
            "security": {
                "secret_key": None,
                "token_encryption_password": None
            }
        }
        
        is_valid, message = ConfigValidator.validate_security_settings(config)
        assert is_valid is False
        assert isinstance(message, str)
