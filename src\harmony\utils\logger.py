"""
Logging configuration and utilities for Harmony bot.
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Optional
import colorlog

from ..config.settings import LoggingConfig, LogLevel


def setup_logging(config: LoggingConfig) -> None:
    """
    Setup logging configuration for the application.
    
    Args:
        config: Logging configuration
    """
    # Create logs directory if it doesn't exist
    log_dir = Path(config.file_path).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.level.value))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatters
    file_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    console_formatter = colorlog.ColoredFormatter(
        fmt='%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s%(reset)s',
        datefmt='%H:%M:%S',
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        filename=config.file_path,
        maxBytes=config.max_file_size,
        backupCount=config.backup_count,
        encoding='utf-8'
    )
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(getattr(logging, config.level.value))
    root_logger.addHandler(file_handler)
    
    # Console handler
    if config.enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(getattr(logging, config.level.value))
        root_logger.addHandler(console_handler)
    
    # Set specific logger levels for noisy libraries
    logging.getLogger('websockets').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance for the given name.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


class StructuredLogger:
    """
    Structured logger that adds context to log messages.
    """
    
    def __init__(self, name: str):
        self.logger = get_logger(name)
        self.context = {}
    
    def add_context(self, **kwargs) -> None:
        """Add context to all future log messages."""
        self.context.update(kwargs)
    
    def remove_context(self, *keys) -> None:
        """Remove context keys."""
        for key in keys:
            self.context.pop(key, None)
    
    def clear_context(self) -> None:
        """Clear all context."""
        self.context.clear()
    
    def _format_message(self, message: str) -> str:
        """Format message with context."""
        if not self.context:
            return message
        
        context_str = " | ".join(f"{k}={v}" for k, v in self.context.items())
        return f"{message} | {context_str}"
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message with context."""
        self.logger.debug(self._format_message(message), extra=kwargs)
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message with context."""
        self.logger.info(self._format_message(message), extra=kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message with context."""
        self.logger.warning(self._format_message(message), extra=kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """Log error message with context."""
        self.logger.error(self._format_message(message), extra=kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """Log critical message with context."""
        self.logger.critical(self._format_message(message), extra=kwargs)
    
    def exception(self, message: str, **kwargs) -> None:
        """Log exception with context."""
        self.logger.exception(self._format_message(message), extra=kwargs)


class LogCapture:
    """
    Context manager for capturing log messages during testing.
    """
    
    def __init__(self, logger_name: str, level: str = "DEBUG"):
        self.logger_name = logger_name
        self.level = getattr(logging, level.upper())
        self.handler = None
        self.records = []
    
    def __enter__(self):
        """Start capturing logs."""
        self.handler = logging.handlers.MemoryHandler(capacity=1000)
        self.handler.setLevel(self.level)
        
        logger = get_logger(self.logger_name)
        logger.addHandler(self.handler)
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Stop capturing logs."""
        if self.handler:
            logger = get_logger(self.logger_name)
            logger.removeHandler(self.handler)
            self.records = self.handler.buffer.copy()
            self.handler.close()
    
    def get_messages(self, level: Optional[str] = None) -> list:
        """
        Get captured log messages.
        
        Args:
            level: Optional level filter
            
        Returns:
            List of log messages
        """
        if level is None:
            return [record.getMessage() for record in self.records]
        
        level_num = getattr(logging, level.upper())
        return [
            record.getMessage() 
            for record in self.records 
            if record.levelno >= level_num
        ]
    
    def has_message(self, message: str, level: Optional[str] = None) -> bool:
        """
        Check if a specific message was logged.
        
        Args:
            message: Message to search for
            level: Optional level filter
            
        Returns:
            True if message was found
        """
        messages = self.get_messages(level)
        return any(message in msg for msg in messages)


def log_function_call(func):
    """
    Decorator to log function calls with arguments and return values.
    """
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        
        # Log function entry
        args_str = ", ".join(str(arg) for arg in args)
        kwargs_str = ", ".join(f"{k}={v}" for k, v in kwargs.items())
        all_args = ", ".join(filter(None, [args_str, kwargs_str]))
        
        logger.debug(f"Calling {func.__name__}({all_args})")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"{func.__name__} returned: {result}")
            return result
        except Exception as e:
            logger.error(f"{func.__name__} raised {type(e).__name__}: {e}")
            raise
    
    return wrapper


def log_async_function_call(func):
    """
    Decorator to log async function calls with arguments and return values.
    """
    async def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        
        # Log function entry
        args_str = ", ".join(str(arg) for arg in args)
        kwargs_str = ", ".join(f"{k}={v}" for k, v in kwargs.items())
        all_args = ", ".join(filter(None, [args_str, kwargs_str]))
        
        logger.debug(f"Calling {func.__name__}({all_args})")
        
        try:
            result = await func(*args, **kwargs)
            logger.debug(f"{func.__name__} returned: {result}")
            return result
        except Exception as e:
            logger.error(f"{func.__name__} raised {type(e).__name__}: {e}")
            raise
    
    return wrapper
