#!/usr/bin/env python3
"""
Quick test to verify our API method fixes are working.
"""

import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_personality_manager_method():
    """Test that PersonalityManager has the correct method."""
    print("Testing PersonalityManager method...")
    
    from harmony.core.personality import PersonalityManager
    from harmony.config.settings import PersonalityConfig
    
    # Create a personality manager
    config = PersonalityConfig()
    pm = PersonalityManager(config)
    
    # Test that get_system_prompt exists and works
    try:
        prompt = pm.get_system_prompt()
        print(f"✅ get_system_prompt() works: {prompt[:50]}...")
        return True
    except AttributeError as e:
        print(f"❌ get_system_prompt() failed: {e}")
        return False

def test_elevenlabs_method():
    """Test that ElevenLabs client has the correct method."""
    print("Testing ElevenLabs method...")
    
    try:
        from elevenlabs import ElevenLabs
        
        # Create client (without API key for method check)
        client = ElevenLabs(api_key="dummy")
        
        # Check if text_to_speech.convert exists
        if hasattr(client, 'text_to_speech') and hasattr(client.text_to_speech, 'convert'):
            print("✅ ElevenLabs text_to_speech.convert() method exists")
            return True
        else:
            print("❌ ElevenLabs text_to_speech.convert() method not found")
            print(f"Available methods: {dir(client)}")
            return False
            
    except Exception as e:
        print(f"❌ ElevenLabs test failed: {e}")
        return False

def test_ai_engine_build_prompt():
    """Test that AI engine can build prompts with personality."""
    print("Testing AI engine prompt building...")
    
    try:
        from harmony.ai.llm_client import AIEngine
        from harmony.core.personality import PersonalityManager
        from harmony.config.settings import AIConfig, PersonalityConfig
        from harmony.core.events import EventBus
        
        # Create components
        ai_config = AIConfig(gemini_api_key="dummy", elevenlabs_api_key="dummy")
        personality_config = PersonalityConfig()
        personality_manager = PersonalityManager(personality_config)
        event_bus = EventBus()
        
        # Create AI engine
        ai_engine = AIEngine(ai_config, personality_manager, event_bus)
        
        # Test prompt building
        prompt = ai_engine._build_prompt("Hello", "TestUser", "TestChannel")
        print(f"✅ AI engine prompt building works: {prompt[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ AI engine test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("="*60)
    print("API METHOD FIX VERIFICATION")
    print("="*60)
    
    tests = [
        test_personality_manager_method,
        test_elevenlabs_method,
        test_ai_engine_build_prompt
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
        print()
    
    passed = sum(results)
    total = len(results)
    
    print("="*60)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All API method fixes are working!")
    else:
        print("⚠️ Some issues remain to be fixed.")
    print("="*60)

if __name__ == "__main__":
    main()
