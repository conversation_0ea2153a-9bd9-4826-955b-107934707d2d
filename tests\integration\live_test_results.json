{"api_connections": {"gemini": {"passed": true, "message": "Google Gemini API connection successful"}, "elevenlabs": {"passed": true, "message": "ElevenLabs API connection successful. User: Unknown"}, "twitch": {"passed": true, "message": "Twitch API credentials are valid"}, "ollama": {"passed": true, "message": "Ollama connection successful. Model 'llama3.2:latest' is available"}}, "ai_engine": {"initialization": true, "response_generation": true, "personality_system": true, "error_handling": true}, "audio_processor": {"initialization": true, "tts_generation": true, "voice_settings": true, "queue_processing": true}, "event_system": {"event_emission": true, "event_handling": true, "ai_response_flow": true, "audio_generation_flow": true}, "end_to_end": {"complete_flow": true, "response_time": 2.140072, "data_integrity": true}}