#!/usr/bin/env python3
"""
Enhanced setup script for Harmony AI Twitch Co-Host <PERSON><PERSON>.
Handles virtual environment creation, dependency installation, and API key configuration.
"""

import subprocess
import sys
import os
import venv
import getpass
from pathlib import Path


class Colors:
    """ANSI color codes for terminal output."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'


def print_colored(message, color=Colors.ENDC):
    """Print colored message to terminal."""
    print(f"{color}{message}{Colors.ENDC}")


def run_command(command, description, cwd=None, capture_output=True):
    """Run a command and handle errors."""
    print_colored(f"🔄 {description}...", Colors.OKBLUE)
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=capture_output, 
            text=True,
            cwd=cwd
        )
        print_colored(f"✅ {description} completed successfully", Colors.OKGREEN)
        return True, result.stdout if capture_output else ""
    except subprocess.CalledProcessError as e:
        print_colored(f"❌ {description} failed:", Colors.FAIL)
        print_colored(f"   Command: {command}", Colors.FAIL)
        if capture_output and e.stderr:
            print_colored(f"   Error: {e.stderr}", Colors.FAIL)
        return False, ""


def check_python_version():
    """Check if Python version is 3.9 or higher."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print_colored(f"❌ Python 3.9+ required, but you have {version.major}.{version.minor}", Colors.FAIL)
        return False
    print_colored(f"✅ Python {version.major}.{version.minor}.{version.micro} detected", Colors.OKGREEN)
    return True


def create_virtual_environment():
    """Create a virtual environment if it doesn't exist."""
    venv_path = Path(".venv")
    
    if venv_path.exists():
        print_colored("✅ Virtual environment already exists", Colors.OKGREEN)
        return True
    
    try:
        print_colored("🔄 Creating virtual environment...", Colors.OKBLUE)
        venv.create(".venv", with_pip=True)
        print_colored("✅ Virtual environment created successfully", Colors.OKGREEN)
        return True
    except Exception as e:
        print_colored(f"❌ Failed to create virtual environment: {e}", Colors.FAIL)
        return False


def get_venv_python():
    """Get the path to the Python executable in the virtual environment."""
    if os.name == 'nt':  # Windows
        return Path(".venv/Scripts/python.exe")
    else:  # Unix/Linux/macOS
        return Path(".venv/bin/python")


def install_dependencies():
    """Install Python dependencies in the virtual environment."""
    python_exe = get_venv_python()
    
    # First, upgrade pip in the virtual environment
    success, _ = run_command(
        f'"{python_exe}" -m pip install --upgrade pip',
        "Upgrading pip in virtual environment"
    )
    if not success:
        return False
    
    # Install core dependencies first
    core_deps = [
        "pydantic>=2.5.0",
        "python-dotenv>=1.0.0", 
        "pyyaml>=6.0.0",
        "click>=8.1.0",
        "requests>=2.31.0",
        "websockets>=12.0",
        "sqlalchemy>=2.0.25",
        "cryptography>=41.0.8",
        "rich>=13.7.0",
        "colorlog>=6.8.0",
        "watchdog>=3.0.0"
    ]
    
    for dep in core_deps:
        success, _ = run_command(
            f'"{python_exe}" -m pip install "{dep}"',
            f"Installing {dep.split('>=')[0]}"
        )
        if not success:
            print_colored(f"⚠️  Failed to install {dep}, continuing...", Colors.WARNING)
    
    # Install AI/LLM dependencies
    ai_deps = [
        "google-genai>=1.18.0",
        "elevenlabs>=0.2.26",
        "twitchio>=2.9.0"
    ]
    
    for dep in ai_deps:
        success, _ = run_command(
            f'"{python_exe}" -m pip install "{dep}"',
            f"Installing {dep.split('>=')[0]}"
        )
        if not success:
            print_colored(f"⚠️  Failed to install {dep}, continuing...", Colors.WARNING)
    
    # Install optional dependencies
    optional_deps = [
        "customtkinter>=5.2.0",
        "SpeechRecognition>=3.10.0",
        "pyttsx3>=2.90"
    ]
    
    print_colored("Installing optional dependencies...", Colors.OKBLUE)
    for dep in optional_deps:
        success, _ = run_command(
            f'"{python_exe}" -m pip install "{dep}"',
            f"Installing {dep.split('>=')[0]} (optional)"
        )
        if not success:
            print_colored(f"⚠️  Optional dependency {dep} failed to install", Colors.WARNING)
    
    return True


def create_directories():
    """Create necessary directories."""
    directories = [
        "data",
        "data/logs", 
        "data/audio",
        "cache",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print_colored("✅ Created necessary directories", Colors.OKGREEN)


def setup_environment_file():
    """Set up environment file with user input."""
    env_file = Path(".env")
    env_example = Path(".env.example")

    if env_file.exists():
        try:
            response = input(f"{Colors.WARNING}⚠️  .env file already exists. Overwrite? (y/N): {Colors.ENDC}")
            if response.lower() != 'y':
                print_colored("✅ Keeping existing .env file", Colors.OKGREEN)
                return
        except (EOFError, KeyboardInterrupt):
            print_colored("\n✅ Keeping existing .env file", Colors.OKGREEN)
            return

    print_colored("\n🔑 API Key Configuration", Colors.HEADER)
    print_colored("Please enter your API keys (press Enter to skip optional ones):", Colors.OKBLUE)
    print_colored("Note: For security, some inputs may not show characters as you type.", Colors.WARNING)

    # Helper function for secure input with fallback
    def secure_input(prompt, required=True, use_getpass=True):
        while True:
            try:
                if use_getpass:
                    try:
                        value = getpass.getpass(prompt).strip()
                    except (EOFError, KeyboardInterrupt):
                        print_colored(f"\n{Colors.WARNING}Input interrupted. Using regular input...{Colors.ENDC}")
                        value = input(prompt.replace("🔹", "🔸")).strip()
                else:
                    value = input(prompt).strip()

                if required and not value:
                    print_colored("❌ This field is required. Please try again.", Colors.FAIL)
                    continue
                return value
            except (EOFError, KeyboardInterrupt):
                if required:
                    print_colored(f"\n❌ Input required. Please try again or press Ctrl+C to exit.", Colors.FAIL)
                    continue
                return ""

    # Required API keys
    google_api_key = secure_input("🔹 Google Gemini API Key (required): ", required=True)
    elevenlabs_api_key = secure_input("🔹 ElevenLabs API Key (required): ", required=True)
    twitch_client_id = secure_input("🔹 Twitch Client ID (required): ", required=True, use_getpass=False)
    twitch_client_secret = secure_input("🔹 Twitch Client Secret (required): ", required=True)

    # Optional API keys
    print_colored("\nOptional API keys:", Colors.OKBLUE)
    perplexity_api_key = secure_input("🔸 Perplexity API Key (optional): ", required=False)

    # Security keys
    print_colored("\nSecurity Configuration:", Colors.OKBLUE)
    secret_key = secure_input("🔹 Secret Key (32+ characters, leave empty to auto-generate): ", required=False)
    if len(secret_key) < 32:
        import secrets
        secret_key = secrets.token_urlsafe(32)
        print_colored(f"✅ Generated secure secret key: {secret_key[:10]}...", Colors.OKGREEN)

    token_password = secure_input("🔹 Token Encryption Password (required): ", required=True)
    
    # Create .env content
    env_content = f"""# Google Gemini API Configuration
GOOGLE_API_KEY={google_api_key}

# ElevenLabs TTS Configuration
ELEVENLABS_API_KEY={elevenlabs_api_key}

# Twitch Application Configuration
TWITCH_CLIENT_ID={twitch_client_id}
TWITCH_CLIENT_SECRET={twitch_client_secret}
TWITCH_REDIRECT_URI=http://localhost:8080/auth/callback

# Optional: Perplexity API for Research
PERPLEXITY_API_KEY={perplexity_api_key}

# Optional: Ollama Configuration (for local models)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# Database Configuration
DATABASE_URL=sqlite:///data/harmony.db

# Security Configuration
SECRET_KEY={secret_key}
TOKEN_ENCRYPTION_PASSWORD={token_password}

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=data/logs/harmony.log

# Audio Configuration
AUDIO_INPUT_DEVICE=default
AUDIO_OUTPUT_DEVICE=default
AUDIO_SAMPLE_RATE=44100

# Feature Toggles
ENABLE_TTS=true
ENABLE_STT=false
ENABLE_TRIVIA=true
ENABLE_MODERATION=true
ENABLE_GOAL_TRACKING=true
ENABLE_REMINDERS=true

# Development Settings
DEBUG=false
DEVELOPMENT_MODE=false
MOCK_TWITCH_API=false
"""
    
    env_file.write_text(env_content)
    print_colored("✅ Created .env file with your configuration", Colors.OKGREEN)


def verify_installation():
    """Verify that key packages are installed."""
    python_exe = get_venv_python()
    
    packages_to_check = [
        ("google.genai", "Google Gemini"),
        ("elevenlabs", "ElevenLabs"), 
        ("twitchio", "TwitchIO"),
        ("pydantic", "Pydantic"),
        ("dotenv", "Python-dotenv")
    ]
    
    failed_imports = []
    
    print_colored("\n🔍 Verifying installation...", Colors.OKBLUE)
    
    for package, name in packages_to_check:
        success, output = run_command(
            f'"{python_exe}" -c "import {package}; print(f\'{name} imported successfully\')"',
            f"Checking {name}"
        )
        if not success:
            failed_imports.append(name)
    
    return len(failed_imports) == 0


def main():
    """Main setup process."""
    print_colored("🚀 Harmony AI Twitch Co-Host Bot Setup", Colors.HEADER)
    print_colored("=" * 50, Colors.HEADER)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create virtual environment
    if not create_virtual_environment():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if not install_dependencies():
        print_colored("❌ Failed to install some dependencies", Colors.FAIL)
        print_colored("You may need to install them manually", Colors.WARNING)
    
    # Setup environment file
    setup_environment_file()
    
    # Verify installation
    if verify_installation():
        print_colored("\n🎉 Setup completed successfully!", Colors.OKGREEN)
        print_colored("\nNext steps:", Colors.OKBLUE)
        print_colored("1. Activate virtual environment:", Colors.ENDC)
        if os.name == 'nt':
            print_colored("   .venv\\Scripts\\activate", Colors.OKCYAN)
        else:
            print_colored("   source .venv/bin/activate", Colors.OKCYAN)
        print_colored("2. Test the installation:", Colors.ENDC)
        print_colored("   python run.py --help", Colors.OKCYAN)
        print_colored("3. Start the bot:", Colors.ENDC)
        print_colored("   python run.py", Colors.OKCYAN)
    else:
        print_colored("\n⚠️  Setup completed with some issues", Colors.WARNING)
        print_colored("Some packages failed to import. Please check the error messages above.", Colors.WARNING)


if __name__ == "__main__":
    main()
