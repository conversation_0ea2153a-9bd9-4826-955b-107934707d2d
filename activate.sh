#!/bin/bash
# Activation script for Unix/Linux/macOS
echo "🚀 Activating Harmony Bot virtual environment..."

if [ ! -d ".venv" ]; then
    echo "❌ Virtual environment not found. Please run setup.py first."
    exit 1
fi

source .venv/bin/activate
echo "✅ Virtual environment activated!"
echo ""
echo "Available commands:"
echo "  python run.py --help     - Show help"
echo "  python run.py            - Start the bot"
echo "  python run.py --debug    - Start in debug mode"
echo "  python run.py --gui      - Start with GUI"
echo ""
exec "$SHELL"
