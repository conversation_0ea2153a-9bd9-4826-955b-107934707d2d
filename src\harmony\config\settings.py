"""
Configuration models using Pydantic for type-safe settings management.
"""

import os
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, field_validator
from enum import Enum


class LogLevel(str, Enum):
    """Logging levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class PersonalityTrait(str, Enum):
    """Available personality traits."""
    CHEERFUL = "cheerful"
    SASSY = "sassy"
    ANALYTICAL = "analytical"
    CALM = "calm"
    ENERGETIC = "energetic"
    HELPFUL = "helpful"
    HUMOROUS = "humorous"
    PROFESSIONAL = "professional"


class SpeakingStyle(BaseModel):
    """Configuration for AI speaking style."""
    formality: str = Field(default="casual", description="Formality level: formal, casual, friendly")
    verbosity: str = Field(default="moderate", description="Response length: brief, moderate, detailed")
    emoji_usage: str = Field(default="moderate", description="Emoji frequency: none, rare, moderate, frequent")
    
    @field_validator('formality')
    @classmethod
    def validate_formality(cls, v):
        if v not in ['formal', 'casual', 'friendly']:
            raise ValueError('formality must be one of: formal, casual, friendly')
        return v

    @field_validator('verbosity')
    @classmethod
    def validate_verbosity(cls, v):
        if v not in ['brief', 'moderate', 'detailed']:
            raise ValueError('verbosity must be one of: brief, moderate, detailed')
        return v


class VoiceSettings(BaseModel):
    """ElevenLabs voice configuration."""
    voice_id: str = Field(default="pNInz6obpgDQGcFmaJgB", description="ElevenLabs voice ID")
    stability: float = Field(default=0.7, ge=0.0, le=1.0, description="Voice stability")
    similarity_boost: float = Field(default=0.8, ge=0.0, le=1.0, description="Similarity boost")
    style: float = Field(default=0.6, ge=0.0, le=1.0, description="Style exaggeration")
    use_speaker_boost: bool = Field(default=True, description="Enable speaker boost")
    speed: float = Field(default=1.0, ge=0.25, le=4.0, description="Speaking speed")


class PersonalityConfig(BaseModel):
    """AI personality configuration."""
    name: str = Field(default="Harmony", description="Bot display name")
    traits: List[PersonalityTrait] = Field(default=[PersonalityTrait.CHEERFUL, PersonalityTrait.HELPFUL])
    speaking_style: SpeakingStyle = Field(default_factory=SpeakingStyle)
    voice_settings: VoiceSettings = Field(default_factory=VoiceSettings)
    system_prompt: Optional[str] = Field(default=None, description="Custom system prompt")
    response_templates: Dict[str, str] = Field(default_factory=dict, description="Custom response templates")


class TwitchConfig(BaseModel):
    """Twitch integration configuration."""
    client_id: str = Field(..., description="Twitch application client ID")
    client_secret: str = Field(..., description="Twitch application client secret")
    redirect_uri: str = Field(default="http://localhost:8080/auth/callback", description="OAuth redirect URI")
    
    # Bot account (optional dedicated bot account)
    bot_username: Optional[str] = Field(default=None, description="Bot account username")
    bot_token: Optional[str] = Field(default=None, description="Bot account OAuth token")
    
    # Streamer account
    streamer_username: Optional[str] = Field(default=None, description="Streamer account username")
    streamer_token: Optional[str] = Field(default=None, description="Streamer account OAuth token")
    
    # Channel settings
    target_channel: Optional[str] = Field(default=None, description="Target channel to monitor")
    command_prefix: str = Field(default="!", description="Command prefix")
    
    @field_validator('redirect_uri')
    @classmethod
    def validate_redirect_uri(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError('redirect_uri must be a valid URL')
        return v


class AIConfig(BaseModel):
    """AI service configuration."""
    # Google Gemini
    gemini_api_key: str = Field(..., description="Google Gemini API key")
    gemini_model: str = Field(default="gemini-2.0-flash-001", description="Gemini model to use")
    
    # ElevenLabs
    elevenlabs_api_key: str = Field(..., description="ElevenLabs API key")
    
    # Optional services
    perplexity_api_key: Optional[str] = Field(default=None, description="Perplexity API key")
    ollama_base_url: str = Field(default="http://localhost:11434", description="Ollama base URL")
    ollama_model: str = Field(default="llama2", description="Ollama model name")
    
    # Rate limiting
    response_cooldown: float = Field(default=2.0, ge=0.0, description="Minimum seconds between AI responses")
    max_tokens: int = Field(default=150, ge=1, le=4000, description="Maximum tokens per response")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="AI response creativity")


class DatabaseConfig(BaseModel):
    """Database configuration."""
    url: str = Field(default="sqlite:///data/harmony.db", description="Database URL")
    encryption_key: Optional[str] = Field(default=None, description="Database encryption key")
    echo: bool = Field(default=False, description="Enable SQL query logging")


class AudioConfig(BaseModel):
    """Audio processing configuration."""
    enabled: bool = Field(default=True, description="Enable audio processing")
    input_device: str = Field(default="default", description="Audio input device")
    output_device: str = Field(default="default", description="Audio output device")
    sample_rate: int = Field(default=44100, description="Audio sample rate")
    enable_tts: bool = Field(default=True, description="Enable text-to-speech")
    enable_stt: bool = Field(default=False, description="Enable speech-to-text")
    tts_rate_limit: int = Field(default=10, ge=1, description="TTS requests per minute")

    # Voice settings
    voice_name: str = Field(default="Rachel", description="ElevenLabs voice name")
    voice_stability: float = Field(default=0.7, ge=0.0, le=1.0, description="Voice stability")
    voice_similarity_boost: float = Field(default=0.8, ge=0.0, le=1.0, description="Voice similarity boost")
    voice_style: float = Field(default=0.6, ge=0.0, le=1.0, description="Voice style exaggeration")
    use_speaker_boost: bool = Field(default=True, description="Enable speaker boost")

    # TTS behavior
    auto_tts_ai_responses: bool = Field(default=True, description="Automatically convert AI responses to speech")
    output_directory: str = Field(default="data/audio", description="Directory for audio output files")

    # Additional test properties
    volume: float = Field(default=0.8, ge=0.0, le=1.0, description="Audio volume")
    voice_id: str = Field(default="pNInz6obpgDQGcFmaJgB", description="ElevenLabs voice ID")
    voice_settings: Dict[str, Any] = Field(default_factory=dict, description="Voice settings dictionary")


class FeatureConfig(BaseModel):
    """Feature toggle configuration."""
    enable_trivia: bool = Field(default=True, description="Enable trivia system")
    enable_moderation: bool = Field(default=True, description="Enable chat moderation")
    enable_goal_tracking: bool = Field(default=True, description="Enable goal tracking")
    enable_reminders: bool = Field(default=True, description="Enable reminder system")
    enable_learning: bool = Field(default=False, description="Enable adaptive learning")


class SecurityConfig(BaseModel):
    """Security and encryption configuration."""
    secret_key: str = Field(..., description="Application secret key")
    token_encryption_password: str = Field(..., description="Token encryption password")
    session_timeout: int = Field(default=3600, ge=300, description="Session timeout in seconds")


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: LogLevel = Field(default=LogLevel.INFO, description="Logging level")
    file_path: str = Field(default="data/logs/harmony.log", description="Log file path")
    max_file_size: int = Field(default=10485760, description="Max log file size in bytes (10MB)")
    backup_count: int = Field(default=5, description="Number of backup log files")
    enable_console: bool = Field(default=True, description="Enable console logging")


class HarmonyConfig(BaseModel):
    """Main configuration model for Harmony bot."""
    
    # Core configurations
    personality: PersonalityConfig = Field(default_factory=PersonalityConfig)
    twitch: TwitchConfig
    ai: AIConfig
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    audio: AudioConfig = Field(default_factory=AudioConfig)
    features: FeatureConfig = Field(default_factory=FeatureConfig)
    security: SecurityConfig
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    
    # Development settings
    debug: bool = Field(default=False, description="Enable debug mode")
    development_mode: bool = Field(default=False, description="Enable development features")
    mock_apis: bool = Field(default=False, description="Use mock APIs for testing")
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    @classmethod
    def from_env(cls) -> "HarmonyConfig":
        """Create configuration from environment variables."""
        return cls(
            twitch=TwitchConfig(
                client_id=os.getenv("TWITCH_CLIENT_ID", ""),
                client_secret=os.getenv("TWITCH_CLIENT_SECRET", ""),
                redirect_uri=os.getenv("TWITCH_REDIRECT_URI", "http://localhost:8080/auth/callback"),
                bot_username=os.getenv("TWITCH_BOT_USERNAME"),
                bot_token=os.getenv("TWITCH_BOT_TOKEN"),
                streamer_username=os.getenv("TWITCH_STREAMER_USERNAME"),
                streamer_token=os.getenv("TWITCH_STREAMER_TOKEN"),
            ),
            ai=AIConfig(
                gemini_api_key=os.getenv("GOOGLE_API_KEY", ""),
                elevenlabs_api_key=os.getenv("ELEVENLABS_API_KEY", ""),
                perplexity_api_key=os.getenv("PERPLEXITY_API_KEY"),
                ollama_base_url=os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
                ollama_model=os.getenv("OLLAMA_MODEL", "llama2"),
            ),
            security=SecurityConfig(
                secret_key=os.getenv("SECRET_KEY", ""),
                token_encryption_password=os.getenv("TOKEN_ENCRYPTION_PASSWORD", ""),
            ),
            debug=os.getenv("DEBUG", "false").lower() == "true",
            development_mode=os.getenv("DEVELOPMENT_MODE", "false").lower() == "true",
        )
