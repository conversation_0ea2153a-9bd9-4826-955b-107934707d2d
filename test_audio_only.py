#!/usr/bin/env python3
"""
Test just the audio processor to isolate the ElevenLabs issue.
"""

import asyncio
import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from harmony.config.manager import ConfigManager
from harmony.core.events import EventBus
from harmony.audio.tts import AudioProcessor
from harmony.utils.logger import get_logger, setup_logging
from harmony.config.settings import LoggingConfig

logger = get_logger(__name__)

async def test_audio_processor():
    """Test audio processor with real configuration."""
    # Setup logging
    log_config = LoggingConfig(level='INFO', enable_console=True)
    setup_logging(log_config)
    
    # Load configuration
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    # Initialize event bus
    event_bus = EventBus()
    await event_bus.start()
    
    try:
        # Create audio processor
        audio_processor = AudioProcessor(config.audio, config.ai, event_bus)
        print(f"Audio processor created: {type(audio_processor)}")
        
        # Initialize and start
        await audio_processor.initialize()
        await audio_processor.start()
        print("Audio processor started successfully")
        
        # Test TTS generation
        print("Testing TTS generation...")
        result = await audio_processor.synthesize_speech("Hello, this is a test of the text to speech system.")
        
        if result:
            print(f"✅ TTS generation successful: {type(result)}")
        else:
            print("❌ TTS generation failed")
        
        # Wait a moment for processing
        await asyncio.sleep(2)
        
        # Stop the processor
        await audio_processor.stop()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await event_bus.stop()

if __name__ == "__main__":
    asyncio.run(test_audio_processor())
