# Harmony AI Twitch Co-Host <PERSON><PERSON> Configuration

# Personality Configuration
personality:
  name: "<PERSON>"
  traits:
    - cheerful
    - helpful
    - humorous
  speaking_style:
    formality: "casual"
    verbosity: "moderate"
    emoji_usage: "moderate"
  voice_settings:
    voice_id: "pNInz6obpgDQGcFmaJgB"  # Adam voice from ElevenLabs
    stability: 0.7
    similarity_boost: 0.8
    style: 0.6
    use_speaker_boost: true
    speed: 1.0
  system_prompt: |
    You are <PERSON>, a friendly and helpful AI co-host for a Twitch stream. 
    You assist the streamer by engaging with chat, answering questions, and 
    providing entertainment. Be conversational, supportive, and maintain a 
    positive atmosphere. Keep responses concise but engaging.

# Audio Configuration
audio:
  enable_tts: true
  enable_stt: false
  input_device: "default"
  output_device: "default"
  sample_rate: 44100
  tts_rate_limit: 10

# Feature Configuration
features:
  enable_trivia: true
  enable_moderation: true
  enable_goal_tracking: true
  enable_reminders: true
  enable_learning: false

# AI Configuration
ai:
  gemini_model: "gemini-2.0-flash-001"
  response_cooldown: 2.0
  max_tokens: 150
  temperature: 0.7

# Database Configuration
database:
  url: "sqlite:///data/harmony.db"
  echo: false

# Logging Configuration
logging:
  level: "INFO"
  file_path: "data/logs/harmony.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5
  enable_console: true

# Development Settings
debug: false
development_mode: false
mock_apis: false
