"""
Input validation utilities for Harmony bot.
"""

import re
from typing import Optional
from urllib.parse import urlparse

from .logger import get_logger

logger = get_logger(__name__)


def validate_twitch_username(username: str) -> bool:
    """
    Validate a Twitch username.
    
    Args:
        username: Username to validate
        
    Returns:
        True if valid Twitch username
    """
    if not username:
        return False
    
    # Twitch usernames: 4-25 characters, alphanumeric + underscore, no consecutive underscores
    pattern = r'^[a-zA-Z0-9][a-zA-Z0-9_]{2,23}[a-zA-Z0-9]$'
    
    # Allow shorter usernames (3-4 chars) without the end constraint
    if len(username) <= 4:
        pattern = r'^[a-zA-Z0-9_]{3,4}$'
    
    if not re.match(pattern, username):
        return False
    
    # Check for consecutive underscores
    if '__' in username:
        return False
    
    return True


def validate_api_key(api_key: str, service: str = "generic") -> bool:
    """
    Validate an API key format.
    
    Args:
        api_key: API key to validate
        service: Service name for specific validation rules
        
    Returns:
        True if API key appears valid
    """
    if not api_key or not isinstance(api_key, str):
        return False
    
    # Remove whitespace
    api_key = api_key.strip()
    
    # Basic length check
    if len(api_key) < 10:
        return False
    
    # Service-specific validation
    if service.lower() == "twitch":
        # Twitch Client ID: 30 characters, alphanumeric
        if len(api_key) == 30 and api_key.isalnum():
            return True
        # Twitch Client Secret: 30 characters, alphanumeric
        if len(api_key) == 30 and api_key.isalnum():
            return True
        return False
    
    elif service.lower() == "google" or service.lower() == "gemini":
        # Google API keys typically start with "AIza" and are 39 characters
        if api_key.startswith("AIza") and len(api_key) == 39:
            return True
        return False
    
    elif service.lower() == "elevenlabs":
        # ElevenLabs API keys are typically 32 characters, alphanumeric
        if len(api_key) == 32 and api_key.replace("-", "").replace("_", "").isalnum():
            return True
        return False
    
    elif service.lower() == "openai":
        # OpenAI API keys start with "sk-" and are longer
        if api_key.startswith("sk-") and len(api_key) > 40:
            return True
        return False
    
    elif service.lower() == "anthropic":
        # Anthropic API keys start with "sk-ant-"
        if api_key.startswith("sk-ant-") and len(api_key) > 50:
            return True
        return False
    
    # Generic validation: reasonable length and contains alphanumeric characters
    if 10 <= len(api_key) <= 200 and any(c.isalnum() for c in api_key):
        return True
    
    return False


def validate_url(url: str) -> bool:
    """
    Validate a URL format.
    
    Args:
        url: URL to validate
        
    Returns:
        True if valid URL
    """
    if not url:
        return False
    
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def validate_oauth_token(token: str) -> bool:
    """
    Validate an OAuth token format.
    
    Args:
        token: OAuth token to validate
        
    Returns:
        True if token appears valid
    """
    if not token:
        return False
    
    # Remove "oauth:" prefix if present
    if token.startswith("oauth:"):
        token = token[6:]
    
    # OAuth tokens are typically 30+ characters, alphanumeric
    if len(token) >= 30 and token.isalnum():
        return True
    
    return False


def validate_email(email: str) -> bool:
    """
    Validate an email address format.
    
    Args:
        email: Email address to validate
        
    Returns:
        True if valid email format
    """
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_port(port: int) -> bool:
    """
    Validate a port number.
    
    Args:
        port: Port number to validate
        
    Returns:
        True if valid port number
    """
    return isinstance(port, int) and 1 <= port <= 65535


def validate_channel_name(channel: str) -> bool:
    """
    Validate a Twitch channel name.
    
    Args:
        channel: Channel name to validate
        
    Returns:
        True if valid channel name
    """
    if not channel:
        return False
    
    # Remove # prefix if present
    if channel.startswith("#"):
        channel = channel[1:]
    
    return validate_twitch_username(channel)


def validate_password_strength(password: str) -> tuple[bool, list[str]]:
    """
    Validate password strength and return feedback.
    
    Args:
        password: Password to validate
        
    Returns:
        Tuple of (is_strong, list_of_issues)
    """
    issues = []
    
    if not password:
        return False, ["Password is required"]
    
    if len(password) < 8:
        issues.append("Password must be at least 8 characters long")
    
    if not any(c.isupper() for c in password):
        issues.append("Password should contain at least one uppercase letter")
    
    if not any(c.islower() for c in password):
        issues.append("Password should contain at least one lowercase letter")
    
    if not any(c.isdigit() for c in password):
        issues.append("Password should contain at least one number")
    
    if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
        issues.append("Password should contain at least one special character")
    
    return len(issues) == 0, issues


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename by removing invalid characters.
    
    Args:
        filename: Filename to sanitize
        
    Returns:
        Sanitized filename
    """
    if not filename:
        return "unnamed"
    
    # Remove invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, "_")
    
    # Remove leading/trailing dots and spaces
    filename = filename.strip(". ")
    
    # Ensure it's not empty
    if not filename:
        filename = "unnamed"
    
    return filename


def validate_json_string(json_str: str) -> bool:
    """
    Validate if a string is valid JSON.
    
    Args:
        json_str: JSON string to validate
        
    Returns:
        True if valid JSON
    """
    if not json_str:
        return False
    
    try:
        import json
        json.loads(json_str)
        return True
    except (json.JSONDecodeError, TypeError):
        return False


def validate_regex_pattern(pattern: str) -> bool:
    """
    Validate if a string is a valid regex pattern.
    
    Args:
        pattern: Regex pattern to validate
        
    Returns:
        True if valid regex pattern
    """
    if not pattern:
        return False
    
    try:
        re.compile(pattern)
        return True
    except re.error:
        return False


def validate_hex_color(color: str) -> bool:
    """
    Validate if a string is a valid hex color code.
    
    Args:
        color: Color code to validate
        
    Returns:
        True if valid hex color
    """
    if not color:
        return False
    
    # Remove # if present
    if color.startswith("#"):
        color = color[1:]
    
    # Check if it's 3 or 6 hex digits
    if len(color) in [3, 6] and all(c in "0123456789abcdefABCDEF" for c in color):
        return True
    
    return False


class ValidationError(Exception):
    """Custom exception for validation errors."""
    
    def __init__(self, message: str, field: Optional[str] = None):
        self.message = message
        self.field = field
        super().__init__(message)


def validate_required_fields(data: dict, required_fields: list[str]) -> None:
    """
    Validate that all required fields are present and not empty.
    
    Args:
        data: Dictionary to validate
        required_fields: List of required field names
        
    Raises:
        ValidationError: If any required field is missing or empty
    """
    missing_fields = []
    empty_fields = []
    
    for field in required_fields:
        if field not in data:
            missing_fields.append(field)
        elif not data[field] or (isinstance(data[field], str) and not data[field].strip()):
            empty_fields.append(field)
    
    if missing_fields:
        raise ValidationError(f"Missing required fields: {', '.join(missing_fields)}")
    
    if empty_fields:
        raise ValidationError(f"Empty required fields: {', '.join(empty_fields)}")
