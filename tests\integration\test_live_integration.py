#!/usr/bin/env python3
"""
Live integration tests that test actual API connections and bot functionality.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime
import json

# Add src directory to Python path
src_path = Path(__file__).parent.parent.parent / "src"
sys.path.insert(0, str(src_path))

from harmony.config.manager import ConfigManager
from harmony.config.validation import validate_all_apis
from harmony.core.events import EventBus, Event, EventType
from harmony.ai.llm_client import AIEngine
from harmony.audio.tts import AudioProcessor
from harmony.utils.logger import get_logger, setup_logging
from harmony.config.settings import LoggingConfig

logger = get_logger(__name__)


class LiveIntegrationTester:
    """Test actual API integrations and component functionality."""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.config = None
        self.event_bus = None
        self.ai_engine = None
        self.audio_processor = None
        self.test_results = {}
    
    async def setup(self):
        """Setup test environment."""
        # Setup logging
        log_config = LoggingConfig(level='INFO', enable_console=True)
        setup_logging(log_config)
        
        # Load configuration
        self.config = self.config_manager.load_config()
        
        # Initialize event bus
        self.event_bus = EventBus()
        await self.event_bus.start()
        
        logger.info("Live integration test environment setup complete")
    
    async def cleanup(self):
        """Cleanup test environment."""
        if self.ai_engine:
            await self.ai_engine.stop()

        if self.audio_processor:
            await self.audio_processor.stop()
        
        if self.event_bus:
            await self.event_bus.stop()
        
        logger.info("Test environment cleaned up")
    
    async def test_api_connections(self) -> dict:
        """Test all API connections."""
        logger.info("Testing API connections...")
        
        config_dict = self.config.model_dump()
        api_results = await validate_all_apis(config_dict)
        
        self.test_results['api_connections'] = api_results
        
        for api_name, (is_valid, message) in api_results.items():
            status = "PASS" if is_valid else "FAIL"
            logger.info(f"  {api_name.title()}: {status} - {message}")
        
        return api_results
    
    async def test_ai_engine_integration(self) -> dict:
        """Test AI engine functionality."""
        logger.info("Testing AI engine integration...")
        
        results = {
            'initialization': False,
            'response_generation': False,
            'personality_system': False,
            'error_handling': False
        }
        
        try:
            # Test initialization - need personality manager
            from harmony.core.personality import PersonalityManager
            personality_manager = PersonalityManager(self.config.personality)
            self.ai_engine = AIEngine(self.config.ai, personality_manager, self.event_bus)
            await self.ai_engine.initialize()
            await self.ai_engine.start()  # Need to start the engine!
            results['initialization'] = True
            logger.info("  AI engine initialization: PASS")
            
            # Test response generation
            test_message = "Hello, how are you today?"
            logger.info(f"  Testing response generation with message: '{test_message}'")
            logger.info(f"  AI Engine running status: {self.ai_engine.is_running}")

            response = await self.ai_engine.generate_response(
                message=test_message,
                username="TestUser",
                channel="TestChannel"
            )

            logger.info(f"  Generated response: {response}")

            if response and len(response) > 0:
                results['response_generation'] = True
                logger.info(f"  Response generation: PASS - '{response[:50]}...'")
            else:
                logger.error("  Response generation: FAIL - No response generated")
            
            # Test personality system
            personality_response = await self.ai_engine.generate_response(
                message="What's your personality like?",
                username="TestUser",
                channel="TestChannel"
            )
            
            if personality_response and "personality" in personality_response.lower():
                results['personality_system'] = True
                logger.info("  Personality system: PASS")
            else:
                logger.info("  Personality system: PARTIAL - Response generated but no personality reference")
                results['personality_system'] = True  # Still consider it working
            
            # Test error handling
            try:
                error_response = await self.ai_engine.generate_response(
                    message="",  # Empty message
                    username="",
                    channel=""
                )
                results['error_handling'] = True
                logger.info("  Error handling: PASS")
            except Exception as e:
                logger.info(f"  Error handling: PASS - Properly caught error: {e}")
                results['error_handling'] = True
            
        except Exception as e:
            logger.error(f"AI engine test failed: {e}")
        
        self.test_results['ai_engine'] = results
        return results
    
    async def test_audio_processor_integration(self) -> dict:
        """Test audio processor functionality."""
        logger.info("Testing audio processor integration...")
        
        results = {
            'initialization': False,
            'tts_generation': False,
            'voice_settings': False,
            'queue_processing': False
        }
        
        try:
            # Test initialization - need both audio and ai config
            self.audio_processor = AudioProcessor(self.config.audio, self.config.ai, self.event_bus)
            await self.audio_processor.initialize()
            await self.audio_processor.start()  # Need to start the processor!
            results['initialization'] = True
            logger.info("  Audio processor initialization: PASS")
            
            # Test TTS generation
            test_text = "Hello, this is a test of the text to speech system."
            tts_result = await self.audio_processor.synthesize_speech(test_text)
            
            if tts_result:
                results['tts_generation'] = True
                logger.info("  TTS generation: PASS")
            else:
                logger.error("  TTS generation: FAIL")
            
            # Test voice settings
            if hasattr(self.audio_processor, '_voice') and self.audio_processor._voice:
                results['voice_settings'] = True
                logger.info(f"  Voice settings: PASS - Using voice: {self.audio_processor._voice.name}")
            else:
                logger.error("  Voice settings: FAIL - No voice configured")
            
            # Test queue processing
            if hasattr(self.audio_processor, '_audio_queue'):
                results['queue_processing'] = True
                logger.info("  Queue processing: PASS")
            else:
                logger.error("  Queue processing: FAIL")
            
        except Exception as e:
            logger.error(f"Audio processor test failed: {e}")
        
        self.test_results['audio_processor'] = results
        return results
    
    async def test_event_system_integration(self) -> dict:
        """Test event system functionality."""
        logger.info("Testing event system integration...")
        
        results = {
            'event_emission': False,
            'event_handling': False,
            'ai_response_flow': False,
            'audio_generation_flow': False
        }
        
        try:
            # Test basic event emission and handling
            received_events = []
            
            async def test_handler(event):
                received_events.append(event)
            
            self.event_bus.subscribe_async(EventType.CHAT_MESSAGE, test_handler)
            
            test_event = Event(
                type=EventType.CHAT_MESSAGE,
                data={
                    "username": "TestUser",
                    "message": "Test message",
                    "channel": "TestChannel",
                    "timestamp": datetime.now().isoformat()
                },
                source="integration_test"
            )
            
            await self.event_bus.emit(test_event)
            await asyncio.sleep(0.1)  # Wait for processing
            
            if len(received_events) > 0:
                results['event_emission'] = True
                results['event_handling'] = True
                logger.info("  Event emission and handling: PASS")
            else:
                logger.error("  Event emission and handling: FAIL")
            
            # Test AI response flow
            if self.ai_engine:
                ai_responses = []
                
                async def ai_response_handler(event):
                    ai_responses.append(event)
                
                self.event_bus.subscribe_async(EventType.AI_RESPONSE, ai_response_handler)
                
                # Simulate chat message that should trigger AI response
                chat_event = Event(
                    type=EventType.CHAT_MESSAGE,
                    data={
                        "username": "TestUser",
                        "message": "@HarmonyBot hello there",
                        "channel": "TestChannel",
                        "timestamp": datetime.now().isoformat()
                    },
                    source="integration_test"
                )
                
                await self.ai_engine._handle_chat_message(chat_event)
                await asyncio.sleep(0.2)  # Wait for AI processing
                
                if len(ai_responses) > 0:
                    results['ai_response_flow'] = True
                    logger.info("  AI response flow: PASS")
                else:
                    logger.error("  AI response flow: FAIL")
            
            # Test audio generation flow
            if self.audio_processor:
                audio_events = []
                
                async def audio_handler(event):
                    audio_events.append(event)
                
                self.event_bus.subscribe_async(EventType.AUDIO_GENERATED, audio_handler)
                
                # Simulate AI response that should trigger audio
                ai_event = Event(
                    type=EventType.AI_RESPONSE,
                    data={
                        "response": "This is a test response for audio generation.",
                        "username": "TestUser",
                        "channel": "TestChannel"
                    },
                    source="integration_test"
                )
                
                await self.audio_processor._handle_ai_response(ai_event)
                await asyncio.sleep(0.3)  # Wait for audio processing
                
                if len(audio_events) > 0:
                    results['audio_generation_flow'] = True
                    logger.info("  Audio generation flow: PASS")
                else:
                    logger.error("  Audio generation flow: FAIL")
            
        except Exception as e:
            logger.error(f"Event system test failed: {e}")
        
        self.test_results['event_system'] = results
        return results
    
    async def test_end_to_end_flow(self) -> dict:
        """Test complete end-to-end message flow."""
        logger.info("Testing end-to-end message flow...")
        
        results = {
            'complete_flow': False,
            'response_time': 0,
            'data_integrity': False
        }
        
        try:
            if not (self.ai_engine and self.audio_processor):
                logger.error("  End-to-end test requires AI engine and audio processor")
                return results
            
            # Track the complete flow
            flow_events = []
            start_time = datetime.now()
            
            async def flow_tracker(event):
                flow_events.append({
                    'type': event.type,
                    'data': event.data,
                    'timestamp': datetime.now()
                })
            
            # Subscribe to all relevant events
            self.event_bus.subscribe_async(EventType.CHAT_MESSAGE, flow_tracker)
            self.event_bus.subscribe_async(EventType.AI_RESPONSE, flow_tracker)
            self.event_bus.subscribe_async(EventType.AUDIO_GENERATED, flow_tracker)
            
            # Start the flow with a chat message
            original_message = "Hello HarmonyBot, how are you doing today?"
            
            chat_event = Event(
                type=EventType.CHAT_MESSAGE,
                data={
                    "username": "IntegrationTest",
                    "message": f"@HarmonyBot {original_message}",
                    "channel": "TestChannel",
                    "timestamp": datetime.now().isoformat()
                },
                source="integration_test"
            )
            
            # Process through AI
            await self.ai_engine._handle_chat_message(chat_event)
            
            # Wait for processing
            await asyncio.sleep(1.0)
            
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            results['response_time'] = response_time
            
            # Analyze the flow
            chat_events = [e for e in flow_events if e['type'] == EventType.CHAT_MESSAGE]
            ai_events = [e for e in flow_events if e['type'] == EventType.AI_RESPONSE]
            audio_events = [e for e in flow_events if e['type'] == EventType.AUDIO_GENERATED]
            
            if len(ai_events) > 0:
                results['complete_flow'] = True
                results['data_integrity'] = True
                logger.info(f"  End-to-end flow: PASS (Response time: {response_time:.2f}s)")
                logger.info(f"  AI Response: '{ai_events[0]['data']['response'][:50]}...'")
                
                if len(audio_events) > 0:
                    logger.info("  Audio generation: PASS")
                else:
                    logger.info("  Audio generation: PARTIAL (may still be processing)")
            else:
                logger.error("  End-to-end flow: FAIL - No AI response generated")
            
        except Exception as e:
            logger.error(f"End-to-end test failed: {e}")
        
        self.test_results['end_to_end'] = results
        return results
    
    async def run_all_tests(self) -> dict:
        """Run all integration tests."""
        logger.info("Starting live integration tests...")
        
        try:
            await self.setup()
            
            # Run all test suites
            await self.test_api_connections()
            await self.test_ai_engine_integration()
            await self.test_audio_processor_integration()
            await self.test_event_system_integration()
            await self.test_end_to_end_flow()
            
            # Generate summary
            self._generate_test_summary()
            
        except Exception as e:
            logger.error(f"Integration test suite failed: {e}")
        finally:
            await self.cleanup()
        
        return self.test_results
    
    def _generate_test_summary(self):
        """Generate and display test summary."""
        logger.info("\n" + "="*60)
        logger.info("LIVE INTEGRATION TEST SUMMARY")
        logger.info("="*60)
        
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.test_results.items():
            if category == 'api_connections':
                logger.info(f"\nAPI Connections:")
                for api, (passed, message) in tests.items():
                    status = "PASS" if passed else "FAIL"
                    logger.info(f"  {api.title()}: {status}")
                    total_tests += 1
                    if passed:
                        passed_tests += 1
            else:
                logger.info(f"\n{category.replace('_', ' ').title()}:")
                for test, passed in tests.items():
                    status = "PASS" if passed else "FAIL"
                    logger.info(f"  {test.replace('_', ' ').title()}: {status}")
                    total_tests += 1
                    if passed:
                        passed_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        logger.info(f"\nOverall Success Rate: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            logger.info("🎉 Integration tests PASSED! Bot is ready for deployment.")
        elif success_rate >= 60:
            logger.info("⚠️  Integration tests PARTIAL. Some issues need attention.")
        else:
            logger.info("❌ Integration tests FAILED. Major issues need to be resolved.")
        
        logger.info("="*60)


async def main():
    """Run live integration tests."""
    tester = LiveIntegrationTester()
    results = await tester.run_all_tests()
    
    # Save results to file
    results_file = Path("tests/integration/live_test_results.json")
    results_file.parent.mkdir(parents=True, exist_ok=True)
    
    # Convert results to JSON-serializable format
    json_results = {}
    for category, tests in results.items():
        if category == 'api_connections':
            json_results[category] = {api: {"passed": passed, "message": message} 
                                    for api, (passed, message) in tests.items()}
        else:
            json_results[category] = tests
    
    with open(results_file, 'w') as f:
        json.dump(json_results, f, indent=2, default=str)
    
    print(f"\nTest results saved to: {results_file}")


if __name__ == "__main__":
    asyncio.run(main())
