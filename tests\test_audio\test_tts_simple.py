#!/usr/bin/env python3
"""
Simplified unit tests for Audio Processor focusing on internal logic.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch

from harmony.audio.tts import AudioProcessor
from harmony.config.settings import AudioConfig, AIConfig
from harmony.core.events import EventBus


class TestAudioProcessorSimple:
    """Simplified test cases for AudioProcessor class."""

    @pytest.fixture
    def audio_config(self):
        """Create test audio configuration."""
        return AudioConfig(
            enabled=True,
            output_device="default",
            volume=0.8,
            voice_name="Rachel",
            voice_stability=0.5,
            voice_similarity_boost=0.5,
            voice_style=0.0,
            use_speaker_boost=True
        )

    @pytest.fixture
    def ai_config(self):
        """Create test AI configuration."""
        return AIConfig(
            elevenlabs_api_key="test_elevenlabs_key",
            gemini_api_key="test_gemini_key"
        )

    @pytest.fixture
    def mock_event_bus(self):
        """Create mock event bus."""
        bus = Mock(spec=EventBus)
        bus.emit = AsyncMock()
        bus.subscribe = Mock()
        bus.subscribe_async = Mock()
        return bus

    @pytest.fixture
    def audio_processor(self, audio_config, ai_config, mock_event_bus):
        """Create AudioProcessor instance for testing."""
        return AudioProcessor(audio_config, ai_config, mock_event_bus)

    def test_audio_processor_initialization(self, audio_processor, audio_config, ai_config):
        """Test AudioProcessor initialization."""
        assert audio_processor.audio_config == audio_config
        assert audio_processor.ai_config == ai_config
        assert audio_processor.event_bus is not None
        assert audio_processor.client is None  # Not initialized yet
        assert audio_processor._running is False

    def test_configuration_access(self, audio_processor):
        """Test configuration access."""
        assert audio_processor.audio_config.enabled is True
        assert audio_processor.audio_config.voice_name == "Rachel"
        assert audio_processor.audio_config.volume == 0.8
        assert audio_processor.ai_config.elevenlabs_api_key == "test_elevenlabs_key"

    def test_state_properties(self, audio_processor):
        """Test state tracking properties."""
        assert audio_processor._initialized is False
        assert audio_processor._running is False
        assert audio_processor._voice is None
        assert audio_processor._voice_settings is None

    def test_is_running_property(self, audio_processor):
        """Test is_running property."""
        assert audio_processor.is_running is False
        
        # Simulate running state
        audio_processor._running = True
        assert audio_processor.is_running is True

    def test_voice_info_property_empty(self, audio_processor):
        """Test voice_info property when no voice is set."""
        voice_info = audio_processor.voice_info
        assert isinstance(voice_info, dict)
        assert voice_info == {}

    def test_voice_info_property_with_voice(self, audio_processor):
        """Test voice_info property with mock voice."""
        # Mock voice and settings
        mock_voice = Mock()
        mock_voice.name = "Rachel"
        mock_voice.voice_id = "test_voice_id"
        mock_voice.category = "premade"
        
        mock_settings = Mock()
        mock_settings.stability = 0.5
        mock_settings.similarity_boost = 0.5
        mock_settings.style = 0.0
        mock_settings.use_speaker_boost = True
        
        audio_processor._voice = mock_voice
        audio_processor._voice_settings = mock_settings
        
        voice_info = audio_processor.voice_info
        assert isinstance(voice_info, dict)
        assert voice_info["name"] == "Rachel"
        assert voice_info["voice_id"] == "test_voice_id"
        assert voice_info["category"] == "premade"
        assert "settings" in voice_info

    def test_audio_queue_initialization(self, audio_processor):
        """Test that audio queue is properly initialized."""
        assert hasattr(audio_processor, '_audio_queue')
        assert audio_processor._audio_queue is not None
        assert audio_processor._processing_task is None

    @pytest.mark.asyncio
    async def test_synthesize_speech_not_running(self, audio_processor):
        """Test speech synthesis when not running."""
        result = await audio_processor.synthesize_speech("Hello, world!")
        assert result is None

    @pytest.mark.asyncio
    async def test_synthesize_speech_no_client(self, audio_processor):
        """Test speech synthesis when client is not initialized."""
        audio_processor._running = True  # Simulate running state
        result = await audio_processor.synthesize_speech("Hello, world!")
        assert result is None

    def test_event_bus_integration(self, audio_processor):
        """Test event bus integration."""
        assert audio_processor.event_bus is not None
        assert hasattr(audio_processor.event_bus, 'emit')
        assert hasattr(audio_processor.event_bus, 'subscribe')

    def test_audio_processor_attributes(self, audio_processor):
        """Test that all expected attributes exist."""
        expected_attrs = [
            'audio_config', 'ai_config', 'event_bus', 'client',
            '_initialized', '_running', '_voice', '_voice_settings',
            '_audio_queue', '_processing_task'
        ]
        
        for attr in expected_attrs:
            assert hasattr(audio_processor, attr), f"Missing attribute: {attr}"

    def test_disabled_audio_config(self):
        """Test behavior with disabled audio configuration."""
        disabled_config = AudioConfig(enabled=False)
        ai_config = AIConfig(elevenlabs_api_key="test", gemini_api_key="test")
        event_bus = Mock(spec=EventBus)
        
        processor = AudioProcessor(disabled_config, ai_config, event_bus)
        assert processor.audio_config.enabled is False

    def test_different_voice_configurations(self):
        """Test different voice configurations."""
        voice_configs = [
            {"voice_name": "Rachel", "voice_stability": 0.5},
            {"voice_name": "Josh", "voice_stability": 0.7},
            {"voice_name": "Arnold", "voice_stability": 0.3}
        ]
        
        for config_data in voice_configs:
            audio_config = AudioConfig(enabled=True, **config_data)
            ai_config = AIConfig(elevenlabs_api_key="test", gemini_api_key="test")
            event_bus = Mock(spec=EventBus)
            
            processor = AudioProcessor(audio_config, ai_config, event_bus)
            assert processor.audio_config.voice_name == config_data["voice_name"]
            assert processor.audio_config.voice_stability == config_data["voice_stability"]

    @pytest.mark.asyncio
    async def test_save_audio_method(self, audio_processor):
        """Test save_audio method."""
        test_audio_data = b"fake_audio_data"
        test_filename = "test_audio.mp3"
        
        # Mock the file operations
        with patch('pathlib.Path.mkdir'), patch('builtins.open', create=True) as mock_open:
            mock_file = Mock()
            mock_open.return_value.__enter__.return_value = mock_file
            
            result = await audio_processor.save_audio(test_audio_data, test_filename)
            
            # Should attempt to save (though mocked)
            mock_open.assert_called()
            mock_file.write.assert_called_with(test_audio_data)

    def test_voice_settings_configuration(self, audio_processor):
        """Test voice settings configuration."""
        config = audio_processor.audio_config
        assert config.voice_stability == 0.5
        assert config.voice_similarity_boost == 0.5
        assert config.voice_style == 0.0
        assert config.use_speaker_boost is True

    @pytest.mark.asyncio
    async def test_queue_operations(self, audio_processor):
        """Test audio queue operations."""
        # Test that queue starts empty
        assert audio_processor._audio_queue.empty()
        
        # Test adding items to queue (when running)
        audio_processor._running = True
        audio_processor.client = Mock()  # Mock client
        audio_processor._voice = Mock()  # Mock voice
        
        # This should add to queue
        await audio_processor.synthesize_speech("Test message")
        
        # Queue should have an item
        assert not audio_processor._audio_queue.empty()

    def test_api_key_validation(self, audio_processor):
        """Test API key validation."""
        assert audio_processor.ai_config.elevenlabs_api_key == "test_elevenlabs_key"
        assert len(audio_processor.ai_config.elevenlabs_api_key) > 0

    @pytest.mark.asyncio
    async def test_stop_without_start(self, audio_processor):
        """Test stopping processor without starting."""
        # Should handle gracefully
        await audio_processor.stop()
        assert audio_processor._running is False
