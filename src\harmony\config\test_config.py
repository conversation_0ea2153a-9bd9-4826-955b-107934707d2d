#!/usr/bin/env python3
"""
Configuration testing utility for validating API connections and settings.
"""

import asyncio
import sys
from pathlib import Path
from typing import Dict, Any

# Add src directory to Python path
src_path = Path(__file__).parent.parent.parent
sys.path.insert(0, str(src_path))

from harmony.config.manager import ConfigManager
from harmony.config.validation import validate_all_apis, ConfigValidator
from harmony.utils.logger import get_logger, setup_logging

logger = get_logger(__name__)


class ConfigTester:
    """Test configuration and API connections."""
    
    def __init__(self, config_path: str = None):
        self.config_manager = ConfigManager(config_path)
        self.config = None
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """
        Run all configuration tests.
        
        Returns:
            Dictionary with test results
        """
        results = {
            'config_loading': False,
            'file_paths': False,
            'security': False,
            'apis': {},
            'overall_status': False,
            'messages': []
        }
        
        try:
            # Test 1: Configuration Loading
            logger.info("Testing configuration loading...")
            self.config = self.config_manager.load_config()
            results['config_loading'] = True
            results['messages'].append("[PASS] Configuration loaded successfully")
            
            config_dict = self.config.model_dump()
            
            # Test 2: File Paths
            logger.info("Testing file paths...")
            file_valid, file_msg = ConfigValidator.validate_file_paths(config_dict)
            results['file_paths'] = file_valid
            if file_valid:
                results['messages'].append(f"[PASS] File paths: {file_msg}")
            else:
                results['messages'].append(f"[FAIL] File paths: {file_msg}")

            # Test 3: Security Settings
            logger.info("Testing security settings...")
            security_valid, security_msg = ConfigValidator.validate_security_settings(config_dict)
            results['security'] = security_valid
            if security_valid:
                results['messages'].append(f"[PASS] Security: {security_msg}")
            else:
                results['messages'].append(f"[FAIL] Security: {security_msg}")
            
            # Test 4: API Connections
            logger.info("Testing API connections...")
            api_results = await validate_all_apis(config_dict)
            results['apis'] = api_results
            
            for api_name, (is_valid, message) in api_results.items():
                if is_valid:
                    results['messages'].append(f"[PASS] {api_name.title()}: {message}")
                else:
                    results['messages'].append(f"[FAIL] {api_name.title()}: {message}")

            # Overall status
            all_apis_valid = all(valid for valid, _ in api_results.values()) if api_results else True
            results['overall_status'] = (
                results['config_loading'] and
                results['file_paths'] and
                results['security'] and
                all_apis_valid
            )

            if results['overall_status']:
                results['messages'].append("[SUCCESS] All tests passed! Configuration is ready.")
            else:
                results['messages'].append("[WARNING] Some tests failed. Please check the issues above.")
            
        except Exception as e:
            logger.error(f"Configuration test failed: {e}")
            results['messages'].append(f"[FAIL] Configuration test failed: {str(e)}")
        
        return results
    
    def print_results(self, results: Dict[str, Any]) -> None:
        """Print test results in a formatted way."""
        print("\n" + "="*60)
        print("HARMONY BOT CONFIGURATION TEST RESULTS")
        print("="*60)
        
        for message in results['messages']:
            print(message)
        
        print("\n" + "-"*60)
        print("DETAILED RESULTS:")
        print(f"   Configuration Loading: {'PASS' if results['config_loading'] else 'FAIL'}")
        print(f"   File Paths:           {'PASS' if results['file_paths'] else 'FAIL'}")
        print(f"   Security Settings:    {'PASS' if results['security'] else 'FAIL'}")

        if results['apis']:
            print("   API Connections:")
            for api_name, (is_valid, _) in results['apis'].items():
                print(f"     - {api_name.title():12}: {'PASS' if is_valid else 'FAIL'}")
        else:
            print("   API Connections:      WARNING - No APIs configured")

        print(f"\n   Overall Status:       {'READY' if results['overall_status'] else 'NEEDS ATTENTION'}")
        print("="*60)
    
    async def test_specific_api(self, api_name: str) -> None:
        """Test a specific API connection."""
        if not self.config:
            self.config = self.config_manager.load_config()
        
        config_dict = self.config.model_dump()
        api_results = await validate_all_apis(config_dict)
        
        if api_name in api_results:
            is_valid, message = api_results[api_name]
            status = "[PASS]" if is_valid else "[FAIL]"
            print(f"{status} {api_name.title()}: {message}")
        else:
            print(f"[FAIL] API '{api_name}' not found or not configured")


async def main():
    """Main entry point for configuration testing."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Harmony Bot configuration")
    parser.add_argument(
        "--config", "-c", 
        help="Path to configuration file"
    )
    parser.add_argument(
        "--api", "-a", 
        help="Test specific API (gemini, elevenlabs, twitch, ollama)"
    )
    parser.add_argument(
        "--verbose", "-v", 
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    from harmony.config.settings import LoggingConfig
    if args.verbose:
        log_config = LoggingConfig(level='DEBUG', enable_console=True, file_path='data/logs/test.log')
    else:
        log_config = LoggingConfig(level='INFO', enable_console=True, file_path='data/logs/test.log')
    setup_logging(log_config)
    
    # Create tester
    tester = ConfigTester(args.config)
    
    if args.api:
        # Test specific API
        await tester.test_specific_api(args.api)
    else:
        # Run all tests
        results = await tester.run_all_tests()
        tester.print_results(results)
        
        # Exit with appropriate code
        sys.exit(0 if results['overall_status'] else 1)


if __name__ == "__main__":
    asyncio.run(main())
