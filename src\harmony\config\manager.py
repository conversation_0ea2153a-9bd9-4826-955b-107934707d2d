"""
Configuration manager for loading, saving, and hot-reloading configuration.
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from .settings import HarmonyConfig
from .validation import validate_all_apis, ConfigValidator
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ConfigFileHandler(FileSystemEventHandler):
    """File system event handler for configuration hot-reloading."""
    
    def __init__(self, config_manager: 'ConfigManager'):
        self.config_manager = config_manager
        
    def on_modified(self, event):
        """Handle file modification events."""
        if not event.is_directory and event.src_path.endswith(('.yaml', '.yml', '.json')):
            logger.info(f"Configuration file changed: {event.src_path}")
            self.config_manager.reload_config()


class ConfigManager:
    """
    Manages configuration loading, saving, and hot-reloading.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config/config.yaml"
        self.config: Optional[HarmonyConfig] = None
        self.observer: Optional[Observer] = None
        self._callbacks: list = []

    @property
    def config_file(self) -> Path:
        """Get config file path as Path object."""
        return Path(self.config_path)

    @property
    def config_dir(self) -> Path:
        """Get config directory path as Path object."""
        return Path(self.config_path).parent
        
    def load_config(self) -> HarmonyConfig:
        """
        Load configuration from file and environment variables.

        Returns:
            HarmonyConfig: Loaded configuration
        """
        try:
            # Ensure config directory exists
            config_dir = os.path.dirname(os.path.abspath(self.config_path))
            os.makedirs(config_dir, exist_ok=True)

            # Start with environment variables
            config_data = self._load_from_env()

            # Override with file configuration if it exists
            if os.path.exists(self.config_path):
                file_data = self._load_from_file(self.config_path)
                config_data = self._merge_configs(config_data, file_data)

            self.config = HarmonyConfig(**config_data)
            logger.info(f"Configuration loaded successfully from {self.config_path}")
            return self.config

        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            # Fallback to environment-only configuration
            self.config = HarmonyConfig.from_env()
            return self.config
    
    def save_config(self, config: HarmonyConfig, path: Optional[str] = None) -> None:
        """
        Save configuration to file.
        
        Args:
            config: Configuration to save
            path: Optional path to save to (defaults to self.config_path)
        """
        save_path = path or self.config_path
        
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # Convert to dict and save
            config_dict = config.model_dump()

            # Convert enums to their values for serialization
            def convert_enums(obj):
                if isinstance(obj, dict):
                    return {k: convert_enums(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_enums(item) for item in obj]
                elif hasattr(obj, 'value'):  # Enum
                    return obj.value
                else:
                    return obj

            config_dict = convert_enums(config_dict)

            if save_path.endswith('.json'):
                with open(save_path, 'w') as f:
                    json.dump(config_dict, f, indent=2)
            else:
                with open(save_path, 'w') as f:
                    yaml.dump(config_dict, f, default_flow_style=False, indent=2)
            
            logger.info(f"Configuration saved to {save_path}")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            raise
    
    def reload_config(self) -> None:
        """Reload configuration and notify callbacks."""
        try:
            old_config = self.config
            new_config = self.load_config()
            
            # Notify callbacks of configuration change
            for callback in self._callbacks:
                try:
                    callback(old_config, new_config)
                except Exception as e:
                    logger.error(f"Error in config change callback: {e}")
                    
        except Exception as e:
            logger.error(f"Failed to reload configuration: {e}")
    
    def start_watching(self) -> None:
        """Start watching configuration file for changes."""
        if self.observer is not None:
            return
            
        config_dir = os.path.dirname(os.path.abspath(self.config_path))
        if not os.path.exists(config_dir):
            logger.warning(f"Configuration directory does not exist: {config_dir}")
            return
            
        self.observer = Observer()
        handler = ConfigFileHandler(self)
        self.observer.schedule(handler, config_dir, recursive=False)
        self.observer.start()
        logger.info(f"Started watching configuration directory: {config_dir}")
    
    def stop_watching(self) -> None:
        """Stop watching configuration file for changes."""
        if self.observer is not None:
            self.observer.stop()
            self.observer.join()
            self.observer = None
            logger.info("Stopped watching configuration files")
    
    def add_change_callback(self, callback) -> None:
        """
        Add a callback to be called when configuration changes.
        
        Args:
            callback: Function that takes (old_config, new_config) as arguments
        """
        self._callbacks.append(callback)
    
    def remove_change_callback(self, callback) -> None:
        """Remove a configuration change callback."""
        if callback in self._callbacks:
            self._callbacks.remove(callback)
    
    def _load_from_env(self) -> Dict[str, Any]:
        """Load configuration from environment variables."""
        return {
            "twitch": {
                "client_id": os.getenv("TWITCH_CLIENT_ID", ""),
                "client_secret": os.getenv("TWITCH_CLIENT_SECRET", ""),
                "redirect_uri": os.getenv("TWITCH_REDIRECT_URI", "http://localhost:8080/auth/callback"),
                "bot_username": os.getenv("TWITCH_BOT_USERNAME"),
                "bot_token": os.getenv("TWITCH_BOT_TOKEN"),
                "streamer_username": os.getenv("TWITCH_STREAMER_USERNAME"),
                "streamer_token": os.getenv("TWITCH_STREAMER_TOKEN"),
            },
            "ai": {
                "gemini_api_key": os.getenv("GOOGLE_API_KEY", ""),
                "elevenlabs_api_key": os.getenv("ELEVENLABS_API_KEY", ""),
                "perplexity_api_key": os.getenv("PERPLEXITY_API_KEY"),
                "ollama_base_url": os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
                "ollama_model": os.getenv("OLLAMA_MODEL", "llama2"),
            },
            "security": {
                "secret_key": os.getenv("SECRET_KEY", ""),
                "token_encryption_password": os.getenv("TOKEN_ENCRYPTION_PASSWORD", ""),
            },
            "debug": os.getenv("DEBUG", "false").lower() == "true",
            "development_mode": os.getenv("DEVELOPMENT_MODE", "false").lower() == "true",
        }
    
    def _load_from_file(self, file_path: str) -> Dict[str, Any]:
        """Load configuration from file."""
        with open(file_path, 'r') as f:
            if file_path.endswith('.json'):
                return json.load(f)
            else:
                return yaml.safe_load(f) or {}
    
    def _merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recursively merge two configuration dictionaries.
        
        Args:
            base: Base configuration
            override: Configuration to merge in (takes precedence)
            
        Returns:
            Merged configuration
        """
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
                
        return result
    
    def get_config(self) -> HarmonyConfig:
        """Get current configuration, loading if necessary."""
        if self.config is None:
            self.load_config()
        return self.config
    
    def update_config(self, **kwargs) -> None:
        """
        Update configuration with new values.
        
        Args:
            **kwargs: Configuration values to update
        """
        if self.config is None:
            self.load_config()
            
        # Create new config with updated values
        config_dict = self.config.model_dump()
        config_dict.update(kwargs)
        
        self.config = HarmonyConfig(**config_dict)
        
        # Save updated configuration
        self.save_config(self.config)
    
    def __enter__(self):
        """Context manager entry."""
        self.load_config()
        self.start_watching()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop_watching()

    def validate_config(self, config: HarmonyConfig) -> tuple[bool, list[str]]:
        """
        Validate configuration.

        Args:
            config: Configuration to validate

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []

        # Basic validation
        if not config.twitch.client_id:
            errors.append("Twitch client ID is required")
        if not config.twitch.client_secret:
            errors.append("Twitch client secret is required")
        if not config.ai.gemini_api_key:
            errors.append("Gemini API key is required")
        if not config.ai.elevenlabs_api_key:
            errors.append("ElevenLabs API key is required")
        if not config.security.secret_key:
            errors.append("Security secret key is required")
        if not config.security.token_encryption_password:
            errors.append("Token encryption password is required")

        return len(errors) == 0, errors

    def backup_config(self, backup_name: Optional[str] = None) -> Path:
        """
        Create a backup of the current configuration.

        Args:
            backup_name: Optional backup filename

        Returns:
            Path to the backup file
        """
        import datetime

        if backup_name is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"config_backup_{timestamp}.yaml"

        backup_path = self.config_dir / backup_name

        # Copy current config file to backup
        if self.config_file.exists():
            import shutil
            shutil.copy2(self.config_file, backup_path)

        return backup_path

    def restore_config(self, backup_path: Path) -> bool:
        """
        Restore configuration from backup.

        Args:
            backup_path: Path to backup file

        Returns:
            True if successful
        """
        try:
            if backup_path.exists():
                import shutil
                shutil.copy2(backup_path, self.config_file)
                self.reload_config()
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to restore config from backup: {e}")
            return False

    def get_config_template(self) -> Dict[str, Any]:
        """
        Get a configuration template with default values.

        Returns:
            Dictionary with template configuration
        """
        return {
            "twitch": {
                "client_id": "",
                "client_secret": "",
                "bot_username": "",
                "streamer_username": ""
            },
            "ai": {
                "gemini_api_key": "",
                "elevenlabs_api_key": "",
                "gemini_model": "gemini-2.0-flash-001",
                "max_tokens": 150,
                "temperature": 0.7
            },
            "audio": {
                "enabled": True,
                "enable_tts": True,
                "enable_stt": False,
                "volume": 0.8,
                "voice_name": "Rachel"
            },
            "security": {
                "secret_key": "",
                "token_encryption_password": ""
            },
            "features": {
                "enable_trivia": True,
                "enable_moderation": True,
                "enable_goal_tracking": True,
                "enable_reminders": True,
                "enable_learning": False
            },
            "logging": {
                "level": "INFO",
                "file_path": "data/logs/harmony.log",
                "enable_console": True
            },
            "debug": False,
            "development_mode": False
        }

    async def validate_configuration(self) -> Dict[str, Any]:
        """
        Validate the current configuration including API connections.

        Returns:
            Dictionary with validation results
        """
        if self.config is None:
            self.load_config()

        config_dict = self.config.model_dump()

        # Validate file paths and security
        file_valid, file_msg = ConfigValidator.validate_file_paths(config_dict)
        security_valid, security_msg = ConfigValidator.validate_security_settings(config_dict)

        # Validate API connections
        api_results = await validate_all_apis(config_dict)

        return {
            'file_paths': (file_valid, file_msg),
            'security': (security_valid, security_msg),
            'apis': api_results,
            'overall_valid': file_valid and security_valid and all(
                valid for valid, _ in api_results.values()
            ) if api_results else file_valid and security_valid
        }
