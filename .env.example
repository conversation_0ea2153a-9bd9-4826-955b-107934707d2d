# Google Gemini API Configuration
GOOGLE_API_KEY=your_gemini_api_key_here

# ElevenLabs TTS Configuration
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Twitch Application Configuration
TWITCH_CLIENT_ID=your_twitch_client_id
TWITCH_CLIENT_SECRET=your_twitch_client_secret
TWITCH_REDIRECT_URI=http://localhost:8080/auth/callback

# Bot Account Configuration (Optional - for dedicated bot account)
TWITCH_BOT_USERNAME=your_bot_username
TWITCH_BOT_TOKEN=oauth:your_bot_token

# Streamer Account Configuration
TWITCH_STREAMER_USERNAME=your_streamer_username
TWITCH_STREAMER_TOKEN=oauth:your_streamer_token

# Optional: Perplexity API for Research
PERPLEXITY_API_KEY=your_perplexity_api_key

# Optional: Ollama Configuration (for local models)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2

# Database Configuration
DATABASE_URL=sqlite:///data/harmony.db
DATABASE_ENCRYPTION_KEY=your_32_character_encryption_key

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=data/logs/harmony.log

# Security Configuration
SECRET_KEY=your_secret_key_for_encryption
TOKEN_ENCRYPTION_PASSWORD=your_token_encryption_password

# Audio Configuration
AUDIO_INPUT_DEVICE=default
AUDIO_OUTPUT_DEVICE=default
AUDIO_SAMPLE_RATE=44100

# GUI Configuration
GUI_THEME=dark
GUI_SCALING=1.0

# Feature Toggles
ENABLE_TTS=true
ENABLE_STT=false
ENABLE_TRIVIA=true
ENABLE_MODERATION=true
ENABLE_GOAL_TRACKING=true
ENABLE_REMINDERS=true

# Rate Limiting
AI_RESPONSE_COOLDOWN=2
TTS_RATE_LIMIT=10
API_RATE_LIMIT=100

# Development Settings
DEBUG=false
DEVELOPMENT_MODE=false
MOCK_TWITCH_API=false
