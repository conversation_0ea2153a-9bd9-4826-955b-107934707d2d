"""
Tests for the event system.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock

from harmony.core.events import EventBus, Event, EventType, create_twitch_message_event


@pytest.mark.unit
class TestEvent:
    """Test the Event class."""
    
    def test_event_creation(self):
        """Test basic event creation."""
        event = Event(
            type=EventType.TWITCH_MESSAGE,
            data={"message": "test"}
        )
        
        assert event.type == EventType.TWITCH_MESSAGE
        assert event.data["message"] == "test"
        assert event.event_id is not None
        assert event.timestamp is not None
    
    def test_event_validation(self):
        """Test event validation."""
        with pytest.raises(ValueError):
            Event(type="invalid_type")


@pytest.mark.unit
class TestEventBus:
    """Test the EventBus class."""
    
    @pytest.mark.asyncio
    async def test_event_bus_lifecycle(self):
        """Test event bus start/stop lifecycle."""
        bus = EventBus()
        
        assert not bus._running
        
        await bus.start()
        assert bus._running
        
        await bus.stop()
        assert not bus._running
    
    @pytest.mark.asyncio
    async def test_sync_handler_subscription(self):
        """Test synchronous handler subscription and execution."""
        bus = EventBus()
        await bus.start()
        
        handler = Mock()
        bus.subscribe(EventType.TWITCH_MESSAGE, handler)
        
        event = Event(type=EventType.TWITCH_MESSAGE, data={"test": "data"})
        await bus.emit_and_wait(event)
        
        handler.assert_called_once_with(event)
        
        await bus.stop()
    
    @pytest.mark.asyncio
    async def test_async_handler_subscription(self):
        """Test asynchronous handler subscription and execution."""
        bus = EventBus()
        await bus.start()
        
        handler = AsyncMock()
        bus.subscribe_async(EventType.TWITCH_MESSAGE, handler)
        
        event = Event(type=EventType.TWITCH_MESSAGE, data={"test": "data"})
        await bus.emit_and_wait(event)
        
        handler.assert_called_once_with(event)
        
        await bus.stop()
    
    @pytest.mark.asyncio
    async def test_multiple_handlers(self):
        """Test multiple handlers for the same event type."""
        bus = EventBus()
        await bus.start()
        
        handler1 = Mock()
        handler2 = Mock()
        bus.subscribe(EventType.TWITCH_MESSAGE, handler1)
        bus.subscribe(EventType.TWITCH_MESSAGE, handler2)
        
        event = Event(type=EventType.TWITCH_MESSAGE, data={"test": "data"})
        await bus.emit_and_wait(event)
        
        handler1.assert_called_once_with(event)
        handler2.assert_called_once_with(event)
        
        await bus.stop()
    
    @pytest.mark.asyncio
    async def test_handler_unsubscription(self):
        """Test handler unsubscription."""
        bus = EventBus()
        await bus.start()
        
        handler = Mock()
        bus.subscribe(EventType.TWITCH_MESSAGE, handler)
        bus.unsubscribe(EventType.TWITCH_MESSAGE, handler)
        
        event = Event(type=EventType.TWITCH_MESSAGE, data={"test": "data"})
        await bus.emit_and_wait(event)
        
        handler.assert_not_called()
        
        await bus.stop()
    
    @pytest.mark.asyncio
    async def test_handler_error_handling(self):
        """Test that handler errors don't crash the event bus."""
        bus = EventBus()
        await bus.start()
        
        def failing_handler(event):
            raise Exception("Handler error")
        
        working_handler = Mock()
        
        bus.subscribe(EventType.TWITCH_MESSAGE, failing_handler)
        bus.subscribe(EventType.TWITCH_MESSAGE, working_handler)
        
        event = Event(type=EventType.TWITCH_MESSAGE, data={"test": "data"})
        await bus.emit_and_wait(event)
        
        # Working handler should still be called despite the failing one
        working_handler.assert_called_once_with(event)
        
        # Check that error was recorded in stats
        stats = bus.get_stats()
        assert stats["handler_errors"] > 0
        
        await bus.stop()
    
    @pytest.mark.asyncio
    async def test_event_queue_processing(self):
        """Test event queue processing."""
        bus = EventBus()
        await bus.start()
        
        handler = Mock()
        bus.subscribe(EventType.TWITCH_MESSAGE, handler)
        
        # Emit multiple events
        events = []
        for i in range(3):
            event = Event(type=EventType.TWITCH_MESSAGE, data={"index": i})
            events.append(event)
            await bus.emit(event)
        
        # Wait a bit for processing
        await asyncio.sleep(0.1)
        
        # All events should have been processed
        assert handler.call_count == 3
        
        await bus.stop()


@pytest.mark.unit
def test_create_twitch_message_event():
    """Test the convenience function for creating Twitch message events."""
    event = create_twitch_message_event(
        username="test_user",
        message="Hello world",
        channel="test_channel"
    )
    
    assert event.type == EventType.TWITCH_MESSAGE
    assert event.data["username"] == "test_user"
    assert event.data["message"] == "Hello world"
    assert event.data["channel"] == "test_channel"
    assert event.source == "twitch"
