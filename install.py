#!/usr/bin/env python3
"""
Installation script for Harmony AI Twitch Co-Host Bo<PERSON>.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is 3.9 or higher."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print(f"❌ Python 3.9+ required, but you have {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True


def create_directories():
    """Create necessary directories."""
    directories = [
        "data",
        "data/logs", 
        "data/audio",
        "cache"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Created necessary directories")


def install_dependencies():
    """Install Python dependencies."""
    commands = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install -r requirements.txt", "Installing dependencies"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    
    return True


def setup_environment():
    """Set up environment file."""
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if not env_file.exists() and env_example.exists():
        env_file.write_text(env_example.read_text())
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env file with your API keys before running the bot")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("⚠️  No .env.example file found")


def verify_installation():
    """Verify that key packages are installed."""
    packages_to_check = [
        "google.genai",
        "elevenlabs", 
        "twitchio",
        "pydantic",
        "customtkinter"
    ]
    
    failed_imports = []
    
    for package in packages_to_check:
        try:
            __import__(package)
            print(f"✅ {package} imported successfully")
        except ImportError:
            failed_imports.append(package)
            print(f"❌ Failed to import {package}")
    
    return len(failed_imports) == 0


def main():
    """Main installation process."""
    print("🚀 Harmony AI Twitch Co-Host Bot Installation")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Verify installation
    print("\n🔍 Verifying installation...")
    if verify_installation():
        print("\n🎉 Installation completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env file with your API keys")
        print("2. Run: python run.py --help")
        print("3. Start the bot: python run.py")
    else:
        print("\n⚠️  Installation completed with some issues")
        print("Some packages failed to import. Please check the error messages above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
