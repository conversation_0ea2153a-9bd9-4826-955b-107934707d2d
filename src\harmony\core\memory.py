"""
Memory management for conversation context and user information.
"""

import json
import sqlite3
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path

from ..config.settings import DatabaseConfig
from ..utils.logger import get_logger
from ..utils.helpers import CircularBuffer

logger = get_logger(__name__)


@dataclass
class UserMemory:
    """Memory about a specific user."""
    username: str
    display_name: str
    first_seen: datetime
    last_seen: datetime
    message_count: int
    is_subscriber: bool
    is_moderator: bool
    is_vip: bool
    preferences: Dict[str, Any]
    notes: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        data = asdict(self)
        data['first_seen'] = self.first_seen.isoformat()
        data['last_seen'] = self.last_seen.isoformat()
        data['preferences'] = json.dumps(self.preferences)
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserMemory':
        """Create from dictionary."""
        data['first_seen'] = datetime.fromisoformat(data['first_seen'])
        data['last_seen'] = datetime.fromisoformat(data['last_seen'])
        data['preferences'] = json.loads(data['preferences']) if data['preferences'] else {}
        return cls(**data)


@dataclass
class ConversationMemory:
    """Memory about a conversation or topic."""
    topic: str
    context: str
    participants: List[str]
    timestamp: datetime
    importance: int  # 1-10 scale
    tags: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['participants'] = json.dumps(self.participants)
        data['tags'] = json.dumps(self.tags)
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversationMemory':
        """Create from dictionary."""
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        data['participants'] = json.loads(data['participants']) if data['participants'] else []
        data['tags'] = json.loads(data['tags']) if data['tags'] else []
        return cls(**data)


class MemoryManager:
    """
    Manages conversation context, user information, and long-term memory.
    """
    
    def __init__(self, config: DatabaseConfig):
        """
        Initialize the memory manager.
        
        Args:
            config: Database configuration
        """
        self.config = config
        self.db_path = self._get_db_path()
        self.conversation_buffer = CircularBuffer(50)  # Recent conversation context
        self.active_topics = {}  # Currently active conversation topics
        
        # Initialize database
        self._init_database()
        
        logger.info(f"Memory manager initialized with database: {self.db_path}")
    
    def _get_db_path(self) -> str:
        """Get the database file path."""
        if self.config.url.startswith("sqlite:///"):
            return self.config.url[10:]  # Remove "sqlite:///"
        return "data/harmony.db"
    
    def _init_database(self) -> None:
        """Initialize the database schema."""
        # Ensure directory exists
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            # Users table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    username TEXT PRIMARY KEY,
                    display_name TEXT,
                    first_seen TEXT,
                    last_seen TEXT,
                    message_count INTEGER DEFAULT 0,
                    is_subscriber BOOLEAN DEFAULT FALSE,
                    is_moderator BOOLEAN DEFAULT FALSE,
                    is_vip BOOLEAN DEFAULT FALSE,
                    preferences TEXT DEFAULT '{}',
                    notes TEXT DEFAULT ''
                )
            """)
            
            # Conversations table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    topic TEXT,
                    context TEXT,
                    participants TEXT,
                    timestamp TEXT,
                    importance INTEGER DEFAULT 5,
                    tags TEXT DEFAULT '[]'
                )
            """)
            
            # Knowledge base table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS knowledge_base (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    question TEXT,
                    answer TEXT,
                    category TEXT,
                    created_at TEXT,
                    updated_at TEXT,
                    usage_count INTEGER DEFAULT 0
                )
            """)
            
            # Stream events table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS stream_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type TEXT,
                    event_data TEXT,
                    timestamp TEXT,
                    processed BOOLEAN DEFAULT FALSE
                )
            """)
            
            conn.commit()
    
    def remember_user(self, username: str, **kwargs) -> None:
        """
        Remember or update information about a user.
        
        Args:
            username: Username to remember
            **kwargs: User information to store
        """
        now = datetime.now()
        
        with sqlite3.connect(self.db_path) as conn:
            # Check if user exists
            cursor = conn.execute("SELECT * FROM users WHERE username = ?", (username,))
            existing = cursor.fetchone()
            
            if existing:
                # Update existing user
                updates = []
                params = []
                
                if 'display_name' in kwargs:
                    updates.append("display_name = ?")
                    params.append(kwargs['display_name'])
                
                if 'is_subscriber' in kwargs:
                    updates.append("is_subscriber = ?")
                    params.append(kwargs['is_subscriber'])
                
                if 'is_moderator' in kwargs:
                    updates.append("is_moderator = ?")
                    params.append(kwargs['is_moderator'])
                
                if 'is_vip' in kwargs:
                    updates.append("is_vip = ?")
                    params.append(kwargs['is_vip'])
                
                updates.append("last_seen = ?")
                params.append(now.isoformat())
                
                updates.append("message_count = message_count + 1")
                params.append(username)
                
                if updates:
                    query = f"UPDATE users SET {', '.join(updates)} WHERE username = ?"
                    conn.execute(query, params)
            else:
                # Create new user
                user_data = {
                    'username': username,
                    'display_name': kwargs.get('display_name', username),
                    'first_seen': now.isoformat(),
                    'last_seen': now.isoformat(),
                    'message_count': 1,
                    'is_subscriber': kwargs.get('is_subscriber', False),
                    'is_moderator': kwargs.get('is_moderator', False),
                    'is_vip': kwargs.get('is_vip', False),
                    'preferences': '{}',
                    'notes': ''
                }
                
                conn.execute("""
                    INSERT INTO users (username, display_name, first_seen, last_seen, 
                                     message_count, is_subscriber, is_moderator, is_vip, 
                                     preferences, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, tuple(user_data.values()))
            
            conn.commit()
    
    def get_user_memory(self, username: str) -> Optional[UserMemory]:
        """
        Get memory about a specific user.
        
        Args:
            username: Username to look up
            
        Returns:
            UserMemory object or None if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM users WHERE username = ?", (username,))
            row = cursor.fetchone()
            
            if row:
                return UserMemory.from_dict(dict(row))
            return None
    
    def remember_conversation(self, topic: str, context: str, participants: List[str], 
                            importance: int = 5, tags: List[str] = None) -> None:
        """
        Remember a conversation or important topic.
        
        Args:
            topic: Topic or subject of the conversation
            context: Context or summary of the conversation
            participants: List of participants
            importance: Importance level (1-10)
            tags: Optional tags for categorization
        """
        if tags is None:
            tags = []
        
        memory = ConversationMemory(
            topic=topic,
            context=context,
            participants=participants,
            timestamp=datetime.now(),
            importance=importance,
            tags=tags
        )
        
        with sqlite3.connect(self.db_path) as conn:
            data = memory.to_dict()
            conn.execute("""
                INSERT INTO conversations (topic, context, participants, timestamp, importance, tags)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (data['topic'], data['context'], data['participants'], 
                  data['timestamp'], data['importance'], data['tags']))
            conn.commit()
        
        logger.debug(f"Remembered conversation: {topic}")
    
    def get_conversation_context(self, limit: int = 10) -> List[ConversationMemory]:
        """
        Get recent conversation context.
        
        Args:
            limit: Maximum number of conversations to return
            
        Returns:
            List of recent conversations
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM conversations 
                ORDER BY timestamp DESC 
                LIMIT ?
            """, (limit,))
            
            return [ConversationMemory.from_dict(dict(row)) for row in cursor.fetchall()]
    
    def search_conversations(self, query: str, limit: int = 5) -> List[ConversationMemory]:
        """
        Search conversations by topic or context.
        
        Args:
            query: Search query
            limit: Maximum results to return
            
        Returns:
            List of matching conversations
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM conversations 
                WHERE topic LIKE ? OR context LIKE ?
                ORDER BY importance DESC, timestamp DESC
                LIMIT ?
            """, (f"%{query}%", f"%{query}%", limit))
            
            return [ConversationMemory.from_dict(dict(row)) for row in cursor.fetchall()]
    
    def add_to_conversation_buffer(self, message: str, username: str, context: Dict[str, Any]) -> None:
        """
        Add a message to the conversation buffer for immediate context.
        
        Args:
            message: Chat message
            username: Username of sender
            context: Additional context
        """
        entry = {
            'message': message,
            'username': username,
            'timestamp': datetime.now().isoformat(),
            'context': context
        }
        
        self.conversation_buffer.add(entry)
    
    def get_recent_context(self, count: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent conversation context from buffer.
        
        Args:
            count: Number of recent messages to return
            
        Returns:
            List of recent conversation entries
        """
        return self.conversation_buffer.get_recent(count)
    
    def store_knowledge(self, question: str, answer: str, category: str = "general") -> None:
        """
        Store knowledge in the knowledge base.
        
        Args:
            question: Question or topic
            answer: Answer or information
            category: Category for organization
        """
        now = datetime.now().isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO knowledge_base (question, answer, category, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            """, (question, answer, category, now, now))
            conn.commit()
        
        logger.debug(f"Stored knowledge: {question}")
    
    def search_knowledge(self, query: str, limit: int = 3) -> List[Tuple[str, str, str]]:
        """
        Search the knowledge base.
        
        Args:
            query: Search query
            limit: Maximum results to return
            
        Returns:
            List of (question, answer, category) tuples
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT question, answer, category FROM knowledge_base
                WHERE question LIKE ? OR answer LIKE ?
                ORDER BY usage_count DESC
                LIMIT ?
            """, (f"%{query}%", f"%{query}%", limit))
            
            results = cursor.fetchall()
            
            # Update usage count for returned results
            for question, _, _ in results:
                conn.execute("""
                    UPDATE knowledge_base 
                    SET usage_count = usage_count + 1 
                    WHERE question = ?
                """, (question,))
            
            conn.commit()
            return results
    
    def get_user_stats(self) -> Dict[str, Any]:
        """Get statistics about users."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total_users,
                    SUM(message_count) as total_messages,
                    COUNT(CASE WHEN is_subscriber THEN 1 END) as subscribers,
                    COUNT(CASE WHEN is_moderator THEN 1 END) as moderators,
                    COUNT(CASE WHEN is_vip THEN 1 END) as vips
                FROM users
            """)
            
            row = cursor.fetchone()
            return {
                'total_users': row[0] or 0,
                'total_messages': row[1] or 0,
                'subscribers': row[2] or 0,
                'moderators': row[3] or 0,
                'vips': row[4] or 0
            }
    
    def cleanup_old_data(self, days: int = 30) -> None:
        """
        Clean up old data from the database.
        
        Args:
            days: Number of days to keep data
        """
        cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            # Clean old conversations (keep important ones longer)
            conn.execute("""
                DELETE FROM conversations 
                WHERE timestamp < ? AND importance < 7
            """, (cutoff_date,))
            
            # Clean old stream events
            conn.execute("""
                DELETE FROM stream_events 
                WHERE timestamp < ? AND processed = TRUE
            """, (cutoff_date,))
            
            conn.commit()
        
        logger.info(f"Cleaned up data older than {days} days")
    
    def export_memory(self, filepath: str) -> None:
        """
        Export memory data to a file.
        
        Args:
            filepath: Path to export file
        """
        data = {
            'users': [],
            'conversations': [],
            'knowledge_base': []
        }
        
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # Export users
            for row in conn.execute("SELECT * FROM users"):
                data['users'].append(dict(row))
            
            # Export conversations
            for row in conn.execute("SELECT * FROM conversations"):
                data['conversations'].append(dict(row))
            
            # Export knowledge base
            for row in conn.execute("SELECT * FROM knowledge_base"):
                data['knowledge_base'].append(dict(row))
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
        
        logger.info(f"Memory exported to {filepath}")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory system statistics."""
        with sqlite3.connect(self.db_path) as conn:
            stats = {}
            
            # User stats
            cursor = conn.execute("SELECT COUNT(*) FROM users")
            stats['total_users'] = cursor.fetchone()[0]
            
            # Conversation stats
            cursor = conn.execute("SELECT COUNT(*) FROM conversations")
            stats['total_conversations'] = cursor.fetchone()[0]
            
            # Knowledge base stats
            cursor = conn.execute("SELECT COUNT(*) FROM knowledge_base")
            stats['knowledge_entries'] = cursor.fetchone()[0]
            
            # Buffer stats
            stats['conversation_buffer_size'] = len(self.conversation_buffer.get_all())
            
            return stats
