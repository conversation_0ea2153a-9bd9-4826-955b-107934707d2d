"""
Pytest configuration and fixtures for Harmony bot tests.
"""

import pytest
import asyncio
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, AsyncMock

# Add src to path for testing
import sys
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from harmony.config.settings import <PERSON><PERSON>onfig, TwitchConfig, AIConfig, SecurityConfig
from harmony.core.events import EventBus
from harmony.core.bot import HarmonyBot


@pytest.fixture
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_config():
    """Create a mock configuration for testing."""
    return HarmonyConfig(
        twitch=TwitchConfig(
            client_id="test_client_id",
            client_secret="test_client_secret",
            redirect_uri="http://localhost:8080/auth/callback"
        ),
        ai=AIConfig(
            gemini_api_key="test_gemini_key",
            elevenlabs_api_key="test_elevenlabs_key"
        ),
        security=SecurityConfig(
            secret_key="test_secret_key_32_characters_long",
            token_encryption_password="test_password"
        )
    )


@pytest.fixture
async def event_bus():
    """Create and start an event bus for testing."""
    bus = EventBus()
    await bus.start()
    yield bus
    await bus.stop()


@pytest.fixture
def temp_config_file():
    """Create a temporary configuration file for testing."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        f.write("""
personality:
  name: "Test Bot"
  traits: ["helpful"]
  
audio:
  enable_tts: false
  enable_stt: false
  
features:
  enable_trivia: false
  enable_moderation: false
  enable_goal_tracking: false
  enable_reminders: false
  
logging:
  level: "DEBUG"
  enable_console: false
""")
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    os.unlink(temp_path)


@pytest.fixture
def mock_twitch_client():
    """Create a mock Twitch client."""
    client = Mock()
    client.initialize = AsyncMock()
    client.start = AsyncMock()
    client.stop = AsyncMock()
    client.send_message = AsyncMock()
    return client


@pytest.fixture
def mock_ai_engine():
    """Create a mock AI engine."""
    engine = Mock()
    engine.initialize = AsyncMock()
    engine.start = AsyncMock()
    engine.stop = AsyncMock()
    engine.generate_response = AsyncMock(return_value="Test response")
    return engine


@pytest.fixture
def mock_audio_processor():
    """Create a mock audio processor."""
    processor = Mock()
    processor.initialize = AsyncMock()
    processor.start = AsyncMock()
    processor.stop = AsyncMock()
    processor.text_to_speech = AsyncMock()
    return processor


@pytest.fixture
async def harmony_bot(mock_config, temp_config_file):
    """Create a Harmony bot instance for testing."""
    # Set environment variables for testing
    os.environ.update({
        "GOOGLE_API_KEY": "test_key",
        "ELEVENLABS_API_KEY": "test_key",
        "TWITCH_CLIENT_ID": "test_id",
        "TWITCH_CLIENT_SECRET": "test_secret",
        "SECRET_KEY": "test_secret_key_32_characters_long",
        "TOKEN_ENCRYPTION_PASSWORD": "test_password"
    })
    
    bot = HarmonyBot(temp_config_file)
    yield bot
    
    # Cleanup
    if bot.is_running():
        await bot.stop()


@pytest.fixture
def sample_twitch_message():
    """Sample Twitch message data for testing."""
    return {
        "username": "test_user",
        "message": "Hello, bot!",
        "channel": "test_channel",
        "timestamp": "2024-01-01T12:00:00Z",
        "user_id": "12345",
        "is_subscriber": False,
        "is_moderator": False,
        "badges": []
    }


@pytest.fixture
def sample_ai_response():
    """Sample AI response data for testing."""
    return {
        "text": "Hello! How can I help you today?",
        "confidence": 0.95,
        "tokens_used": 15,
        "model": "gemini-2.0-flash-001",
        "processing_time": 0.5
    }


# Pytest markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.slow = pytest.mark.slow
