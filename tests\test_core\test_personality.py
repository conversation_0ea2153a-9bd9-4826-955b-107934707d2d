#!/usr/bin/env python3
"""
Unit tests for Personality Manager.
"""

import pytest
from unittest.mock import Mock

from harmony.core.personality import PersonalityManager
from harmony.config.settings import PersonalityConfig, PersonalityTrait


class TestPersonalityManager:
    """Test cases for PersonalityManager class."""

    @pytest.fixture
    def personality_config(self):
        """Create test personality configuration."""
        return PersonalityConfig(
            name="TestBot",
            traits=[PersonalityTrait.HELPFUL, PersonalityTrait.CHEERFUL]
        )

    @pytest.fixture
    def personality_manager(self, personality_config):
        """Create PersonalityManager instance for testing."""
        return PersonalityManager(personality_config)

    def test_personality_manager_initialization(self, personality_manager, personality_config):
        """Test PersonalityManager initialization."""
        assert personality_manager.config == personality_config
        assert personality_manager.config.name == "TestBot"
        assert PersonalityTrait.HELPFUL in personality_manager.config.traits
        assert PersonalityTrait.CHEERFUL in personality_manager.config.traits

    def test_get_system_prompt(self, personality_manager):
        """Test system prompt generation."""
        prompt = personality_manager.get_system_prompt()
        
        assert isinstance(prompt, str)
        assert len(prompt) > 0
        assert "TestBot" in prompt or "helpful" in prompt.lower()

    def test_get_system_prompt_with_custom(self):
        """Test system prompt with custom prompt."""
        config = PersonalityConfig(
            name="CustomBot",
            traits=[PersonalityTrait.PROFESSIONAL],
            system_prompt="You are a custom AI assistant."
        )
        manager = PersonalityManager(config)
        
        prompt = manager.get_system_prompt()
        assert "You are a custom AI assistant." in prompt

    def test_personality_traits_integration(self, personality_manager):
        """Test that personality traits are properly integrated."""
        prompt = personality_manager.get_system_prompt()
        
        # Should contain references to the configured traits or their synonyms
        prompt_lower = prompt.lower()
        # The personality system uses synonyms like "supportive" for helpful and "upbeat" for cheerful
        assert ("helpful" in prompt_lower or "supportive" in prompt_lower or
                "cheerful" in prompt_lower or "upbeat" in prompt_lower or "positive" in prompt_lower)

    def test_different_personality_configurations(self):
        """Test different personality configurations."""
        configs = [
            PersonalityConfig(
                name="SassyBot",
                traits=[PersonalityTrait.SASSY, PersonalityTrait.HUMOROUS]
            ),
            PersonalityConfig(
                name="AnalyticalBot", 
                traits=[PersonalityTrait.ANALYTICAL, PersonalityTrait.PROFESSIONAL]
            ),
            PersonalityConfig(
                name="CalmBot",
                traits=[PersonalityTrait.CALM, PersonalityTrait.HELPFUL]
            )
        ]
        
        for config in configs:
            manager = PersonalityManager(config)
            prompt = manager.get_system_prompt()
            
            assert isinstance(prompt, str)
            assert len(prompt) > 0
            assert config.name in prompt

    def test_personality_manager_methods(self, personality_manager):
        """Test that all expected methods exist."""
        expected_methods = ['get_system_prompt']
        
        for method in expected_methods:
            assert hasattr(personality_manager, method)
            assert callable(getattr(personality_manager, method))

    def test_config_access(self, personality_manager):
        """Test configuration access."""
        assert personality_manager.config is not None
        assert hasattr(personality_manager.config, 'name')
        assert hasattr(personality_manager.config, 'traits')

    def test_empty_traits_handling(self):
        """Test handling of empty traits list."""
        config = PersonalityConfig(
            name="MinimalBot",
            traits=[]
        )
        manager = PersonalityManager(config)
        
        # Should still generate a valid prompt
        prompt = manager.get_system_prompt()
        assert isinstance(prompt, str)
        assert len(prompt) > 0

    def test_single_trait_handling(self):
        """Test handling of single trait."""
        config = PersonalityConfig(
            name="SingleTraitBot",
            traits=[PersonalityTrait.ENERGETIC]
        )
        manager = PersonalityManager(config)
        
        prompt = manager.get_system_prompt()
        assert isinstance(prompt, str)
        assert "energetic" in prompt.lower() or "SingleTraitBot" in prompt

    def test_all_personality_traits(self):
        """Test all available personality traits."""
        all_traits = [
            PersonalityTrait.CHEERFUL,
            PersonalityTrait.SASSY,
            PersonalityTrait.ANALYTICAL,
            PersonalityTrait.CALM,
            PersonalityTrait.ENERGETIC,
            PersonalityTrait.HELPFUL,
            PersonalityTrait.HUMOROUS,
            PersonalityTrait.PROFESSIONAL
        ]
        
        for trait in all_traits:
            config = PersonalityConfig(
                name=f"{trait.value.title()}Bot",
                traits=[trait]
            )
            manager = PersonalityManager(config)
            prompt = manager.get_system_prompt()
            
            assert isinstance(prompt, str)
            assert len(prompt) > 0
