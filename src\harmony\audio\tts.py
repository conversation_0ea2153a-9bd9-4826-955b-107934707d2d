"""
Audio processor for TTS with voice synthesis and audio streaming.
"""

import asyncio
import io
from typing import Optional, Dict, Any
from datetime import datetime
from pathlib import Path

from elevenlabs import ElevenLabs, Voice, VoiceSettings
from elevenlabs.types import Model

from ..config.settings import AudioConfig, AIConfig
from ..core.events import EventBus, Event, EventType, create_system_event
from ..utils.logger import get_logger

logger = get_logger(__name__)


class AudioProcessor:
    """
    Audio processor that handles TTS with voice synthesis and audio streaming.
    """
    
    def __init__(self, audio_config: AudioConfig, ai_config: AIConfig, event_bus: EventBus):
        """
        Initialize the Audio Processor.
        
        Args:
            audio_config: Audio configuration
            ai_config: AI configuration (for ElevenLabs API key)
            event_bus: Event bus for communication
        """
        self.audio_config = audio_config
        self.ai_config = ai_config
        self.event_bus = event_bus
        
        # ElevenLabs client
        self.client: Optional[ElevenLabs] = None
        
        # State tracking
        self._initialized = False
        self._running = False
        
        # Voice settings
        self._voice: Optional[Voice] = None
        self._voice_settings: Optional[VoiceSettings] = None
        
        # Audio queue for processing
        self._audio_queue: asyncio.Queue = asyncio.Queue()
        self._processing_task: Optional[asyncio.Task] = None
        
    async def initialize(self) -> None:
        """Initialize the Audio Processor."""
        try:
            logger.info("Initializing Audio Processor...")
            
            # Validate configuration
            if not self.ai_config.elevenlabs_api_key:
                raise ValueError("ElevenLabs API key is required")
            
            # Initialize ElevenLabs client
            self.client = ElevenLabs(api_key=self.ai_config.elevenlabs_api_key)
            
            # Set up voice and settings
            await self._setup_voice()
            
            # Test the connection
            await self._test_connection()
            
            # Subscribe to relevant events
            await self._setup_event_handlers()
            
            self._initialized = True
            logger.info("Audio Processor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Audio Processor: {e}")
            raise
    
    async def start(self) -> None:
        """Start the Audio Processor."""
        if not self._initialized:
            raise RuntimeError("Audio Processor not initialized")
        
        try:
            logger.info("Starting Audio Processor...")
            
            # Start audio processing task
            self._processing_task = asyncio.create_task(self._process_audio_queue())
            
            self._running = True
            
            # Emit started event
            await self.event_bus.emit(create_system_event(
                EventType.AUDIO_PROCESSOR_STARTED,
                {"voice_id": self._voice.voice_id if self._voice else None}
            ))
            
            logger.info("Audio Processor started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start Audio Processor: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the Audio Processor."""
        try:
            logger.info("Stopping Audio Processor...")
            
            self._running = False
            
            # Cancel processing task
            if self._processing_task:
                self._processing_task.cancel()
                try:
                    await self._processing_task
                except asyncio.CancelledError:
                    pass
            
            # Clear audio queue
            while not self._audio_queue.empty():
                try:
                    self._audio_queue.get_nowait()
                except asyncio.QueueEmpty:
                    break
            
            # Emit stopped event
            await self.event_bus.emit(create_system_event(
                EventType.AUDIO_PROCESSOR_STOPPED,
                {}
            ))
            
            logger.info("Audio Processor stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping Audio Processor: {e}")
    
    async def _setup_voice(self) -> None:
        """Set up voice and voice settings."""
        try:
            # Get available voices
            voices = self.client.voices.get_all()
            
            # Use configured voice or default
            voice_name = self.audio_config.voice_name or "Rachel"
            
            # Find the voice
            for voice in voices.voices:
                if voice.name.lower() == voice_name.lower():
                    self._voice = voice
                    break
            
            if not self._voice and voices.voices:
                # Use first available voice as fallback
                self._voice = voices.voices[0]
                logger.warning(f"Voice '{voice_name}' not found, using '{self._voice.name}'")
            
            if not self._voice:
                raise Exception("No voices available")
            
            # Set up voice settings
            self._voice_settings = VoiceSettings(
                stability=self.audio_config.voice_stability,
                similarity_boost=self.audio_config.voice_similarity_boost,
                style=self.audio_config.voice_style,
                use_speaker_boost=self.audio_config.use_speaker_boost
            )
            
            logger.info(f"Voice setup complete: {self._voice.name}")
            
        except Exception as e:
            logger.error(f"Error setting up voice: {e}")
            raise
    
    async def _test_connection(self) -> None:
        """Test the connection to ElevenLabs API."""
        try:
            # Simple test - get voices to verify API connection
            voices = self.client.voices.get_all()
            if voices:
                logger.info(f"ElevenLabs API connection test successful ({len(voices.voices)} voices available)")
            else:
                raise Exception("Failed to get voices from ElevenLabs API")
                
        except Exception as e:
            logger.error(f"ElevenLabs API connection test failed: {e}")
            raise
    
    async def _setup_event_handlers(self) -> None:
        """Set up event handlers for audio processing."""
        # Subscribe to AI responses for TTS
        self.event_bus.subscribe_async(EventType.AI_RESPONSE, self._handle_ai_response)

        # Subscribe to TTS requests
        self.event_bus.subscribe_async(EventType.TTS_REQUEST, self._handle_tts_request)
    
    async def _handle_ai_response(self, event: Event) -> None:
        """Handle AI responses for TTS conversion."""
        if not self._running or not self.audio_config.enable_tts:
            return
        
        try:
            response_data = event.data
            response_text = response_data.get("response", "")
            
            if response_text and self.audio_config.auto_tts_ai_responses:
                await self.synthesize_speech(response_text)
                
        except Exception as e:
            logger.error(f"Error handling AI response for TTS: {e}")
    
    async def _handle_tts_request(self, event: Event) -> None:
        """Handle direct TTS requests."""
        if not self._running:
            return
        
        try:
            request_data = event.data
            text = request_data.get("text", "")
            
            if text:
                await self.synthesize_speech(text)
                
        except Exception as e:
            logger.error(f"Error handling TTS request: {e}")
    
    async def synthesize_speech(self, text: str) -> Optional[bytes]:
        """
        Synthesize speech from text.
        
        Args:
            text: Text to synthesize
            
        Returns:
            Audio data as bytes or None if failed
        """
        if not self.client or not self._voice or not self._running:
            return None
        
        try:
            # Add to processing queue
            await self._audio_queue.put({
                "text": text,
                "timestamp": datetime.now()
            })
            
            logger.debug(f"Added TTS request to queue: {text[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"Error synthesizing speech: {e}")
            return None
    
    async def _process_audio_queue(self) -> None:
        """Process audio synthesis queue."""
        logger.info("Audio processing queue started")
        
        while self._running:
            try:
                # Wait for audio request
                audio_request = await asyncio.wait_for(
                    self._audio_queue.get(),
                    timeout=1.0
                )
                
                text = audio_request["text"]
                
                # Generate audio
                audio_data = await self._generate_audio(text)
                
                if audio_data:
                    # Emit audio generated event
                    await self.event_bus.emit(Event(
                        type=EventType.AUDIO_GENERATED,
                        data={
                            "text": text,
                            "audio_size": len(audio_data),
                            "voice": self._voice.name,
                            "timestamp": datetime.now().isoformat()
                        },
                        source="audio_processor",
                        timestamp=datetime.now()
                    ))
                    
                    # Here you could save the audio or stream it
                    # For now, we'll just log it
                    logger.info(f"Generated audio for: {text[:50]}...")
                
            except asyncio.TimeoutError:
                # No requests in queue, continue
                continue
            except Exception as e:
                logger.error(f"Error processing audio queue: {e}")
                await asyncio.sleep(1)
        
        logger.info("Audio processing queue stopped")
    
    async def _generate_audio(self, text: str) -> Optional[bytes]:
        """Generate audio from text using ElevenLabs."""
        try:
            # Generate audio using the correct API method
            response = self.client.text_to_speech.convert(
                text=text,
                voice_id=self._voice.voice_id,
                voice_settings=self._voice_settings,
                model_id="eleven_multilingual_v2",
                output_format="mp3_44100_128"
            )

            # Collect audio data from the response
            audio_data = b""
            for chunk in response:
                audio_data += chunk

            return audio_data

        except Exception as e:
            logger.error(f"Error generating audio with ElevenLabs: {e}")
            return None
    
    async def save_audio(self, audio_data: bytes, filename: str) -> bool:
        """
        Save audio data to file.
        
        Args:
            audio_data: Audio data as bytes
            filename: Output filename
            
        Returns:
            True if saved successfully
        """
        try:
            output_path = Path(self.audio_config.output_directory) / filename
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, "wb") as f:
                f.write(audio_data)
            
            logger.info(f"Audio saved to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving audio: {e}")
            return False
    
    @property
    def is_running(self) -> bool:
        """Check if the Audio Processor is running."""
        return self._running
    
    @property
    def voice_info(self) -> Dict[str, Any]:
        """Get information about the current voice."""
        if self._voice:
            return {
                "name": self._voice.name,
                "voice_id": self._voice.voice_id,
                "category": self._voice.category,
                "settings": {
                    "stability": self._voice_settings.stability if self._voice_settings else None,
                    "similarity_boost": self._voice_settings.similarity_boost if self._voice_settings else None,
                    "style": self._voice_settings.style if self._voice_settings else None,
                    "use_speaker_boost": self._voice_settings.use_speaker_boost if self._voice_settings else None
                }
            }
        return {}
