#!/usr/bin/env python3
"""
Unit tests for utility helper functions.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
import json

from harmony.utils.helpers import (
    format_uptime, sanitize_message, truncate_text, parse_duration,
    format_timestamp, extract_mentions, extract_urls, chunk_list,
    safe_get, merge_dicts, retry_async, CircularBuffer, EventCounter,
    sanitize_filename, validate_url, format_file_size, is_valid_email,
    generate_random_string, deep_merge_dicts, safe_json_loads
)


class TestHelpers:
    """Test cases for utility helper functions."""

    def test_format_timestamp_default(self):
        """Test timestamp formatting with default format."""
        dt = datetime(2024, 1, 15, 14, 30, 45)
        result = format_timestamp(dt)
        assert isinstance(result, str)
        assert "2024" in result
        assert "14:30" in result or "2:30" in result

    def test_format_timestamp_custom_format(self):
        """Test timestamp formatting with custom format."""
        dt = datetime(2024, 1, 15, 14, 30, 45)
        result = format_timestamp(dt, "%Y-%m-%d")
        assert result == "2024-01-15"

    def test_parse_duration_seconds(self):
        """Test duration parsing for seconds."""
        assert parse_duration("30s") == 30
        assert parse_duration("45s") == 45

    def test_parse_duration_minutes(self):
        """Test duration parsing for minutes."""
        assert parse_duration("5m") == 300  # 5 * 60
        assert parse_duration("10m") == 600

    def test_parse_duration_hours(self):
        """Test duration parsing for hours."""
        assert parse_duration("2h") == 7200  # 2 * 3600
        assert parse_duration("1h") == 3600

    def test_parse_duration_invalid(self):
        """Test duration parsing with invalid input."""
        assert parse_duration("invalid") == 0
        assert parse_duration("") == 0
        assert parse_duration("123") == 0  # No unit

    def test_sanitize_filename_basic(self):
        """Test basic filename sanitization."""
        result = sanitize_filename("test file.txt")
        assert result == "test_file.txt"

    def test_sanitize_filename_special_chars(self):
        """Test filename sanitization with special characters."""
        result = sanitize_filename("file<>:\"|?*.txt")
        assert "<" not in result
        assert ">" not in result
        assert ":" not in result
        assert "|" not in result
        assert "?" not in result
        assert "*" not in result

    def test_sanitize_filename_empty(self):
        """Test filename sanitization with empty input."""
        result = sanitize_filename("")
        assert result == "untitled"

    def test_truncate_text_short(self):
        """Test text truncation with short text."""
        text = "Short text"
        result = truncate_text(text, 50)
        assert result == text

    def test_truncate_text_long(self):
        """Test text truncation with long text."""
        text = "This is a very long text that should be truncated"
        result = truncate_text(text, 20)
        assert len(result) <= 23  # 20 + "..."
        assert result.endswith("...")

    def test_truncate_text_exact_length(self):
        """Test text truncation with exact length."""
        text = "Exactly twenty chars"
        result = truncate_text(text, 20)
        assert result == text

    def test_validate_url_valid(self):
        """Test URL validation with valid URLs."""
        valid_urls = [
            "https://example.com",
            "http://test.org",
            "https://sub.domain.com/path",
            "http://localhost:8080"
        ]
        
        for url in valid_urls:
            assert validate_url(url) is True

    def test_validate_url_invalid(self):
        """Test URL validation with invalid URLs."""
        invalid_urls = [
            "not-a-url",
            "ftp://example.com",  # Wrong protocol
            "",
            "https://",
            "just text"
        ]
        
        for url in invalid_urls:
            assert validate_url(url) is False

    def test_extract_mentions_basic(self):
        """Test mention extraction from text."""
        text = "Hello @user1 and @user2!"
        mentions = extract_mentions(text)
        assert "user1" in mentions
        assert "user2" in mentions
        assert len(mentions) == 2

    def test_extract_mentions_none(self):
        """Test mention extraction with no mentions."""
        text = "Hello everyone!"
        mentions = extract_mentions(text)
        assert len(mentions) == 0

    def test_extract_mentions_duplicate(self):
        """Test mention extraction with duplicate mentions."""
        text = "Hello @user1 and @user1 again!"
        mentions = extract_mentions(text)
        assert len(mentions) == 1
        assert "user1" in mentions

    def test_format_file_size_bytes(self):
        """Test file size formatting for bytes."""
        assert format_file_size(512) == "512 B"
        assert format_file_size(1023) == "1023 B"

    def test_format_file_size_kb(self):
        """Test file size formatting for kilobytes."""
        assert format_file_size(1024) == "1.0 KB"
        assert format_file_size(2048) == "2.0 KB"

    def test_format_file_size_mb(self):
        """Test file size formatting for megabytes."""
        assert format_file_size(1024 * 1024) == "1.0 MB"
        assert format_file_size(1024 * 1024 * 2) == "2.0 MB"

    def test_format_file_size_gb(self):
        """Test file size formatting for gigabytes."""
        assert format_file_size(1024 * 1024 * 1024) == "1.0 GB"

    def test_is_valid_email_valid(self):
        """Test email validation with valid emails."""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in valid_emails:
            assert is_valid_email(email) is True

    def test_is_valid_email_invalid(self):
        """Test email validation with invalid emails."""
        invalid_emails = [
            "not-an-email",
            "@example.com",
            "user@",
            "",
            "user <EMAIL>"
        ]
        
        for email in invalid_emails:
            assert is_valid_email(email) is False

    def test_generate_random_string_length(self):
        """Test random string generation with different lengths."""
        for length in [5, 10, 20, 50]:
            result = generate_random_string(length)
            assert len(result) == length
            assert isinstance(result, str)

    def test_generate_random_string_uniqueness(self):
        """Test that random strings are unique."""
        strings = [generate_random_string(10) for _ in range(10)]
        assert len(set(strings)) == 10  # All should be unique

    def test_deep_merge_dicts_basic(self):
        """Test basic dictionary merging."""
        dict1 = {"a": 1, "b": 2}
        dict2 = {"c": 3, "d": 4}
        result = deep_merge_dicts(dict1, dict2)
        
        assert result["a"] == 1
        assert result["b"] == 2
        assert result["c"] == 3
        assert result["d"] == 4

    def test_deep_merge_dicts_nested(self):
        """Test nested dictionary merging."""
        dict1 = {"a": {"x": 1, "y": 2}}
        dict2 = {"a": {"z": 3}, "b": 4}
        result = deep_merge_dicts(dict1, dict2)
        
        assert result["a"]["x"] == 1
        assert result["a"]["y"] == 2
        assert result["a"]["z"] == 3
        assert result["b"] == 4

    def test_deep_merge_dicts_override(self):
        """Test dictionary merging with value override."""
        dict1 = {"a": 1, "b": {"x": 1}}
        dict2 = {"a": 2, "b": {"x": 2}}
        result = deep_merge_dicts(dict1, dict2)
        
        assert result["a"] == 2  # Should be overridden
        assert result["b"]["x"] == 2  # Should be overridden

    def test_safe_json_loads_valid(self):
        """Test safe JSON loading with valid JSON."""
        json_str = '{"key": "value", "number": 42}'
        result = safe_json_loads(json_str)
        
        assert isinstance(result, dict)
        assert result["key"] == "value"
        assert result["number"] == 42

    def test_safe_json_loads_invalid(self):
        """Test safe JSON loading with invalid JSON."""
        invalid_json = '{"key": "value", invalid}'
        result = safe_json_loads(invalid_json)
        
        assert result is None

    def test_safe_json_loads_empty(self):
        """Test safe JSON loading with empty string."""
        result = safe_json_loads("")
        assert result is None

    @pytest.mark.asyncio
    async def test_retry_async_success(self):
        """Test async retry decorator with successful function."""
        call_count = 0
        
        @retry_async(max_attempts=3, delay=0.01)
        async def test_func():
            nonlocal call_count
            call_count += 1
            return "success"
        
        result = await test_func()
        assert result == "success"
        assert call_count == 1

    @pytest.mark.asyncio
    async def test_retry_async_eventual_success(self):
        """Test async retry decorator with eventual success."""
        call_count = 0
        
        @retry_async(max_attempts=3, delay=0.01)
        async def test_func():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return "success"
        
        result = await test_func()
        assert result == "success"
        assert call_count == 3

    @pytest.mark.asyncio
    async def test_retry_async_max_attempts(self):
        """Test async retry decorator reaching max attempts."""
        call_count = 0
        
        @retry_async(max_attempts=2, delay=0.01)
        async def test_func():
            nonlocal call_count
            call_count += 1
            raise Exception("Always fails")
        
        with pytest.raises(Exception, match="Always fails"):
            await test_func()
        
        assert call_count == 2
