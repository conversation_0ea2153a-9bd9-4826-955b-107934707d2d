"""
Main bot orchestrator that coordinates all subsystems.
"""

import asyncio
import signal
from typing import Optional, Dict, Any
from datetime import datetime

from ..config.settings import HarmonyConfig
from ..config.manager import ConfigManager
from ..utils.logger import get_logger, setup_logging
from .events import EventBus, Event, EventType, create_system_event
from .personality import PersonalityManager
from .memory import MemoryManager

logger = get_logger(__name__)


class HarmonyBot:
    """
    Main bot class that orchestrates all subsystems and manages the bot lifecycle.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the Harmony bot.
        
        Args:
            config_path: Optional path to configuration file
        """
        self.config_manager = ConfigManager(config_path)
        self.config: Optional[HarmonyConfig] = None
        self.event_bus = EventBus()
        
        # Core managers
        self.personality_manager: Optional[PersonalityManager] = None
        self.memory_manager: Optional[MemoryManager] = None
        
        # Subsystem managers (will be initialized later)
        self.twitch_client = None
        self.ai_engine = None
        self.audio_processor = None
        self.gui_manager = None
        
        # State tracking
        self._running = False
        self._startup_time: Optional[datetime] = None
        self._shutdown_handlers = []
        
        # Statistics
        self._stats = {
            "messages_processed": 0,
            "ai_responses_generated": 0,
            "errors_encountered": 0,
            "uptime_seconds": 0,
        }
    
    async def initialize(self) -> None:
        """Initialize all bot subsystems."""
        try:
            logger.info("Initializing Harmony bot...")
            
            # Load configuration
            self.config = self.config_manager.load_config()
            
            # Setup logging based on configuration
            setup_logging(self.config.logging)
            
            # Initialize event bus
            await self.event_bus.start()
            
            # Initialize core managers
            self.personality_manager = PersonalityManager(self.config.personality)
            self.memory_manager = MemoryManager(self.config.database)
            
            # Subscribe to configuration changes
            self.config_manager.add_change_callback(self._on_config_changed)
            
            # Initialize subsystems
            await self._initialize_subsystems()
            
            # Setup signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            logger.info("Harmony bot initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize bot: {e}")
            raise
    
    async def start(self) -> None:
        """Start the bot and all subsystems."""
        if self._running:
            logger.warning("Bot is already running")
            return
        
        try:
            logger.info("Starting Harmony bot...")
            self._running = True
            self._startup_time = datetime.now()
            
            # Start subsystems
            await self._start_subsystems()
            
            # Emit startup event
            startup_event = create_system_event(
                EventType.SYSTEM_STARTUP,
                "Harmony bot started successfully",
                startup_time=self._startup_time
            )
            await self.event_bus.emit(startup_event)
            
            logger.info("Harmony bot started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start bot: {e}")
            self._running = False
            raise
    
    async def stop(self) -> None:
        """Stop the bot and all subsystems gracefully."""
        if not self._running:
            return
        
        try:
            logger.info("Stopping Harmony bot...")
            self._running = False
            
            # Emit shutdown event
            shutdown_event = create_system_event(
                EventType.SYSTEM_SHUTDOWN,
                "Harmony bot shutting down",
                uptime_seconds=self.get_uptime_seconds()
            )
            await self.event_bus.emit(shutdown_event)
            
            # Run shutdown handlers
            for handler in self._shutdown_handlers:
                try:
                    await handler()
                except Exception as e:
                    logger.error(f"Error in shutdown handler: {e}")
            
            # Stop subsystems
            await self._stop_subsystems()
            
            # Stop core systems
            await self.event_bus.stop()
            self.config_manager.stop_watching()
            
            logger.info("Harmony bot stopped successfully")
            
        except Exception as e:
            logger.error(f"Error during bot shutdown: {e}")
    
    async def run(self) -> None:
        """Run the bot until stopped."""
        await self.initialize()
        await self.start()
        
        try:
            # Keep running until stopped
            while self._running:
                await asyncio.sleep(1)
                self._update_stats()
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        finally:
            await self.stop()
    
    def is_running(self) -> bool:
        """Check if the bot is currently running."""
        return self._running
    
    def get_uptime_seconds(self) -> int:
        """Get bot uptime in seconds."""
        if self._startup_time is None:
            return 0
        return int((datetime.now() - self._startup_time).total_seconds())
    
    def get_stats(self) -> Dict[str, Any]:
        """Get bot statistics."""
        stats = self._stats.copy()
        stats["uptime_seconds"] = self.get_uptime_seconds()
        stats["event_bus_stats"] = self.event_bus.get_stats()
        return stats
    
    def add_shutdown_handler(self, handler) -> None:
        """Add a handler to be called during shutdown."""
        self._shutdown_handlers.append(handler)
    
    async def _initialize_subsystems(self) -> None:
        """Initialize all bot subsystems."""
        # Import here to avoid circular imports
        from ..twitch.client import TwitchClient
        from ..ai.llm_client import AIEngine
        from ..audio.tts import AudioProcessor
        
        # Initialize Twitch client
        if self.config.twitch.client_id and self.config.twitch.client_secret:
            self.twitch_client = TwitchClient(self.config.twitch, self.event_bus)
            await self.twitch_client.initialize()
        else:
            logger.warning("Twitch credentials not configured, skipping Twitch client")
        
        # Initialize AI engine
        if self.config.ai.gemini_api_key:
            self.ai_engine = AIEngine(self.config.ai, self.personality_manager, self.event_bus)
            await self.ai_engine.initialize()
        else:
            logger.warning("AI credentials not configured, skipping AI engine")
        
        # Initialize audio processor
        if self.config.audio.enable_tts and self.config.ai.elevenlabs_api_key:
            try:
                self.audio_processor = AudioProcessor(self.config.audio, self.config.ai, self.event_bus)
                await self.audio_processor.initialize()
                logger.info("Audio processor initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize audio processor: {e}")
                logger.warning("Continuing without audio/TTS functionality")
                self.audio_processor = None
        else:
            logger.warning("Audio/TTS not configured, skipping audio processor")
    
    async def _start_subsystems(self) -> None:
        """Start all initialized subsystems."""
        if self.twitch_client:
            await self.twitch_client.start()
        
        if self.ai_engine:
            await self.ai_engine.start()
        
        if self.audio_processor:
            await self.audio_processor.start()
    
    async def _stop_subsystems(self) -> None:
        """Stop all subsystems."""
        if self.audio_processor:
            await self.audio_processor.stop()
        
        if self.ai_engine:
            await self.ai_engine.stop()
        
        if self.twitch_client:
            await self.twitch_client.stop()
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def _on_config_changed(self, old_config: HarmonyConfig, new_config: HarmonyConfig) -> None:
        """Handle configuration changes."""
        logger.info("Configuration changed, updating subsystems...")
        self.config = new_config
        
        # Update personality manager
        if self.personality_manager:
            self.personality_manager.update_config(new_config.personality)
        
        # Emit configuration change event
        config_event = create_system_event(
            EventType.CONFIG_CHANGED,
            "Configuration updated",
            old_config=old_config.dict(),
            new_config=new_config.dict()
        )
        asyncio.create_task(self.event_bus.emit(config_event))
    
    def _update_stats(self) -> None:
        """Update bot statistics."""
        self._stats["uptime_seconds"] = self.get_uptime_seconds()
        
        # Update stats from event bus
        event_stats = self.event_bus.get_stats()
        self._stats["messages_processed"] = event_stats.get("events_processed", 0)
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop()
