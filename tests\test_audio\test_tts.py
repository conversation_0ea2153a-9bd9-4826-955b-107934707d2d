#!/usr/bin/env python3
"""
Unit tests for Audio Processor and TTS functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from pathlib import Path

from harmony.audio.tts import AudioProcessor
from harmony.config.settings import AudioConfig, AIConfig
from harmony.core.events import EventBus


class TestAudioProcessor:
    """Test cases for AudioProcessor class."""

    @pytest.fixture
    def audio_config(self):
        """Create test audio configuration."""
        return AudioConfig(
            enabled=True,
            output_device="default",
            volume=0.8,
            voice_id="test_voice_id",
            voice_settings={
                "stability": 0.5,
                "similarity_boost": 0.5,
                "style": 0.0,
                "use_speaker_boost": True
            }
        )

    @pytest.fixture
    def ai_config(self):
        """Create test AI configuration."""
        return AIConfig(
            elevenlabs_api_key="test_elevenlabs_key",
            gemini_api_key="test_gemini_key"
        )

    @pytest.fixture
    def mock_event_bus(self):
        """Create mock event bus."""
        bus = Mock(spec=EventBus)
        bus.emit = AsyncMock()
        bus.subscribe = Mock()
        return bus

    @pytest.fixture
    def audio_processor(self, audio_config, ai_config, mock_event_bus):
        """Create AudioProcessor instance for testing."""
        return AudioProcessor(audio_config, ai_config, mock_event_bus)

    def test_audio_processor_initialization(self, audio_processor, audio_config):
        """Test AudioProcessor initialization."""
        assert audio_processor.audio_config == audio_config
        assert audio_processor.ai_config is not None
        assert audio_processor.event_bus is not None
        assert audio_processor.client is None  # Not initialized yet
        assert audio_processor._running is False

    @pytest.mark.asyncio
    async def test_audio_processor_lifecycle(self, audio_processor):
        """Test AudioProcessor start/stop lifecycle."""
        # Mock the ElevenLabs client at the module level where it's imported
        with patch('harmony.audio.tts.ElevenLabs') as mock_client_class:
            mock_client = Mock()
            mock_voice = Mock()
            mock_voice.voice_id = "test_voice_id"
            mock_voice.name = "Rachel"
            mock_client.voices.get_all.return_value = Mock(voices=[mock_voice])
            mock_client_class.return_value = mock_client

            # Initialize
            await audio_processor.initialize()
            assert audio_processor.client is not None

            # Start
            await audio_processor.start()
            assert audio_processor._running is True

            # Stop
            await audio_processor.stop()
            assert audio_processor._running is False

    @pytest.mark.asyncio
    async def test_synthesize_speech_success(self, audio_processor):
        """Test successful speech synthesis."""
        with patch('harmony.audio.tts.ElevenLabs') as mock_client_class:
            # Mock the client and voice
            mock_client = Mock()
            mock_voice = Mock()
            mock_voice.voice_id = "test_voice_id"
            mock_voice.name = "Rachel"
            mock_client.voices.get_all.return_value = Mock(voices=[mock_voice])
            
            # Mock the TTS conversion
            mock_audio_data = b"fake_audio_data"
            mock_client.text_to_speech.convert.return_value = iter([mock_audio_data])
            mock_client_class.return_value = mock_client

            # Initialize and start
            await audio_processor.initialize()
            await audio_processor.start()

            # Test speech synthesis
            result = await audio_processor.synthesize_speech("Hello, world!", direct=True)

            assert result is not None
            assert isinstance(result, bytes)
            audio_processor.event_bus.emit.assert_called()

    @pytest.mark.asyncio
    async def test_synthesize_speech_failure(self, audio_processor):
        """Test speech synthesis failure handling."""
        with patch('harmony.audio.tts.ElevenLabs') as mock_client_class:
            # Mock the client and voice
            mock_client = Mock()
            mock_voice = Mock()
            mock_voice.voice_id = "test_voice_id"
            mock_voice.name = "Rachel"
            mock_client.voices.get_all.return_value = Mock(voices=[mock_voice])
            
            # Mock TTS conversion to raise an exception
            mock_client.text_to_speech.convert.side_effect = Exception("TTS Error")
            mock_client_class.return_value = mock_client

            # Initialize and start
            await audio_processor.initialize()
            await audio_processor.start()

            # Test speech synthesis failure
            result = await audio_processor.synthesize_speech("Hello, world!", direct=True)

            assert result is None

    @pytest.mark.asyncio
    async def test_test_connection_success(self, audio_processor):
        """Test successful connection testing."""
        with patch('harmony.audio.tts.ElevenLabs') as mock_client_class:
            mock_client = Mock()
            mock_voice = Mock()
            mock_voice.voice_id = "test_voice_id"
            mock_voice.name = "Rachel"
            mock_client.voices.get_all.return_value = Mock(voices=[mock_voice])
            mock_client_class.return_value = mock_client

            await audio_processor.initialize()
            result = await audio_processor.test_connection()
            
            assert result is True

    @pytest.mark.asyncio
    async def test_test_connection_failure(self, audio_processor):
        """Test connection testing failure."""
        with patch('harmony.audio.tts.ElevenLabs') as mock_client_class:
            mock_voice = Mock()
            mock_voice.voice_id = "test_voice_id"
            mock_voice.name = "Rachel"

            mock_client = Mock()
            # Three calls during initialization: _setup_voice, _test_connection, then test_connection fails
            mock_client.voices.get_all.side_effect = [
                Mock(voices=[mock_voice]),  # Success for _setup_voice
                Mock(voices=[mock_voice]),  # Success for _test_connection during init
                Exception("Connection failed")  # Failure for test_connection
            ]
            mock_client_class.return_value = mock_client

            await audio_processor.initialize()
            result = await audio_processor.test_connection()

            assert result is False

    def test_voice_configuration(self, audio_processor):
        """Test voice configuration setup."""
        with patch('harmony.audio.tts.ElevenLabs') as mock_client_class:
            mock_client = Mock()
            mock_voice = Mock()
            mock_voice.voice_id = "test_voice_id"
            mock_voice.name = "Rachel"
            mock_client.voices.get_all.return_value = Mock(voices=[mock_voice])
            mock_client_class.return_value = mock_client

            # Test voice setup during initialization
            asyncio.run(audio_processor.initialize())
            
            assert audio_processor._voice is not None
            assert audio_processor._voice_settings is not None

    @pytest.mark.asyncio
    async def test_audio_queue_processing(self, audio_processor):
        """Test audio queue processing functionality."""
        with patch('harmony.audio.tts.ElevenLabs') as mock_client_class:
            mock_client = Mock()
            mock_voice = Mock()
            mock_voice.voice_id = "test_voice_id"
            mock_voice.name = "Rachel"
            mock_client.voices.get_all.return_value = Mock(voices=[mock_voice])
            
            # Mock successful TTS
            mock_audio_data = b"fake_audio_data"
            mock_client.text_to_speech.convert.return_value = iter([mock_audio_data])
            mock_client_class.return_value = mock_client

            await audio_processor.initialize()
            await audio_processor.start()

            # Add multiple items to queue
            await audio_processor.synthesize_speech("First message")
            await audio_processor.synthesize_speech("Second message")

            # Give some time for queue processing
            await asyncio.sleep(0.1)

            # Verify events were emitted
            assert audio_processor.event_bus.emit.call_count >= 2

    def test_audio_processor_disabled(self):
        """Test behavior when audio is disabled."""
        disabled_config = AudioConfig(enabled=False)
        ai_config = AIConfig(elevenlabs_api_key="test_key", gemini_api_key="test_key")
        event_bus = Mock(spec=EventBus)

        processor = AudioProcessor(disabled_config, ai_config, event_bus)

        # Should handle gracefully when disabled
        assert processor.audio_config.enabled is False

    @pytest.mark.asyncio
    async def test_voice_not_found(self, audio_processor):
        """Test behavior when specified voice is not found."""
        with patch('harmony.audio.tts.ElevenLabs') as mock_client_class:
            mock_client = Mock()
            # Return empty voice list
            mock_client.voices.get_all.return_value = Mock(voices=[])
            mock_client_class.return_value = mock_client

            # Should raise an exception when no voices are available
            try:
                await audio_processor.initialize()
                assert False, "Expected exception when no voices available"
            except Exception as e:
                assert "No voices available" in str(e)

    @pytest.mark.asyncio
    async def test_event_emission(self, audio_processor):
        """Test that events are properly emitted."""
        with patch('harmony.audio.tts.ElevenLabs') as mock_client_class:
            mock_client = Mock()
            mock_voice = Mock()
            mock_voice.voice_id = "test_voice_id"
            mock_voice.name = "Rachel"
            mock_client.voices.get_all.return_value = Mock(voices=[mock_voice])
            
            mock_audio_data = b"fake_audio_data"
            mock_client.text_to_speech.convert.return_value = iter([mock_audio_data])
            mock_client_class.return_value = mock_client

            await audio_processor.initialize()
            await audio_processor.start()

            await audio_processor.synthesize_speech("Test message")

            # Verify events were emitted
            assert audio_processor.event_bus.emit.call_count >= 1
            
            # Give more time for audio processing
            await asyncio.sleep(0.2)

            # Check that audio generation event was emitted
            calls = audio_processor.event_bus.emit.call_args_list
            event_types = [call[0][0].type for call in calls]

            # Import EventType for comparison
            from harmony.core.events import EventType
            assert EventType.AUDIO_GENERATED in event_types
