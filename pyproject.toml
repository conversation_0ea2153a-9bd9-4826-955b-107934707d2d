[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "harmony-bot"
version = "0.1.0"
description = "AI Twitch Co-Host Bot with Google Gemini and ElevenLabs integration"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Blu3Hrt", email = "<EMAIL>"},
]
maintainers = [
    {name = "Blu3Hrt", email = "<EMAIL>"},
]
keywords = ["twitch", "ai", "chatbot", "streaming", "gemini", "elevenlabs"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Communications :: Chat",
    "Topic :: Games/Entertainment",
    "Topic :: Multimedia :: Sound/Audio :: Speech",
]
requires-python = ">=3.9"
dependencies = [
    "asyncio-mqtt>=0.16.0",
    "pydantic>=2.5.0",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0.0",
    "click>=8.1.0",
    "twitchio>=2.9.0",
    "requests>=2.31.0",
    "websockets>=12.0",
    "google-genai>=0.3.0",
    "elevenlabs>=0.2.20",
    "customtkinter>=5.2.0",
    "sqlalchemy>=2.0.20",
    "alembic>=1.13.0",
    "cryptography>=41.0.0",
    "schedule>=1.2.0",
    "colorlog>=6.8.0",
    "rich>=13.7.0",
    "httpx>=0.26.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.1.0",
    "black>=23.12.0",
    "flake8>=7.0.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0",
]
audio = [
    "speech-recognition>=3.10.0",
    "pyttsx3>=2.90",
    "pyaudio>=0.2.14",
]
ai-extras = [
    "anthropic>=0.8.0",
    "openai>=1.6.0",
    "ollama>=0.1.7",
]
analysis = [
    "numpy>=1.26.0",
    "scipy>=1.12.0",
    "matplotlib>=3.8.0",
]

[project.urls]
Homepage = "https://github.com/Blu3Hrt/harmony-bot"
Documentation = "https://github.com/Blu3Hrt/harmony-bot/docs"
Repository = "https://github.com/Blu3Hrt/harmony-bot.git"
Issues = "https://github.com/Blu3Hrt/harmony-bot/issues"

[project.scripts]
harmony = "harmony.main:main"
harmony-gui = "harmony.gui.main:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "twitchio.*",
    "elevenlabs.*",
    "customtkinter.*",
    "pyaudio.*",
    "speech_recognition.*",
    "pyttsx3.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src/harmony"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/conftest.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".eggs",
    "*.egg",
]
