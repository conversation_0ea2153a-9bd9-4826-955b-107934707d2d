"""
AI Engine for LLM integration with personality and memory management.
"""

import asyncio
from typing import Optional, Dict, Any, List
from datetime import datetime

import google.genai as genai
from google.genai.types import GenerateContentConfig

from ..config.settings import AIConfig
from ..core.events import EventBus, Event, EventType, create_system_event
from ..core.personality import PersonalityManager
from ..utils.logger import get_logger

logger = get_logger(__name__)


class AIEngine:
    """
    AI Engine that handles LLM integration with personality and memory management.
    """
    
    def __init__(self, config: AIConfig, personality_manager: PersonalityManager, event_bus: EventBus):
        """
        Initialize the AI Engine.
        
        Args:
            config: AI configuration
            personality_manager: Personality manager for context
            event_bus: Event bus for communication
        """
        self.config = config
        self.personality_manager = personality_manager
        self.event_bus = event_bus
        
        # Gemini client
        self.client: Optional[genai.Client] = None
        
        # State tracking
        self._initialized = False
        self._running = False
        
        # Conversation context
        self._conversation_history: List[Dict[str, Any]] = []
        self._max_history_length = 50
        
    async def initialize(self) -> None:
        """Initialize the AI Engine."""
        try:
            logger.info("Initializing AI Engine...")
            
            # Validate configuration
            if not self.config.gemini_api_key:
                raise ValueError("Google Gemini API key is required")
            
            # Initialize Gemini client
            self.client = genai.Client(api_key=self.config.gemini_api_key)
            
            # Test the connection
            await self._test_connection()
            
            # Subscribe to relevant events
            await self._setup_event_handlers()
            
            self._initialized = True
            logger.info("AI Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize AI Engine: {e}")
            raise
    
    async def start(self) -> None:
        """Start the AI Engine."""
        if not self._initialized:
            raise RuntimeError("AI Engine not initialized")
        
        try:
            logger.info("Starting AI Engine...")
            
            self._running = True
            
            # Emit started event
            await self.event_bus.emit(create_system_event(
                EventType.AI_ENGINE_STARTED,
                {"model": self.config.gemini_model}
            ))
            
            logger.info("AI Engine started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start AI Engine: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the AI Engine."""
        try:
            logger.info("Stopping AI Engine...")
            
            self._running = False
            
            # Clear conversation history
            self._conversation_history.clear()
            
            # Emit stopped event
            await self.event_bus.emit(create_system_event(
                EventType.AI_ENGINE_STOPPED,
                {}
            ))
            
            logger.info("AI Engine stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping AI Engine: {e}")
    
    async def _test_connection(self) -> None:
        """Test the connection to Gemini API."""
        try:
            # Simple test request
            response = await self.client.aio.models.generate_content(
                model=self.config.gemini_model,
                contents="Hello, this is a test message.",
                config=GenerateContentConfig(
                    max_output_tokens=10,
                    temperature=0.1
                )
            )
            
            if response.text:
                logger.info("Gemini API connection test successful")
            else:
                raise Exception("Empty response from Gemini API")
                
        except Exception as e:
            logger.error(f"Gemini API connection test failed: {e}")
            raise
    
    async def _setup_event_handlers(self) -> None:
        """Set up event handlers for AI processing."""
        # Subscribe to chat messages for AI responses
        self.event_bus.subscribe_async(EventType.CHAT_MESSAGE, self._handle_chat_message)

        # Subscribe to AI requests
        self.event_bus.subscribe_async(EventType.AI_REQUEST, self._handle_ai_request)
    
    async def _handle_chat_message(self, event: Event) -> None:
        """Handle incoming chat messages for AI processing."""
        if not self._running:
            return
        
        try:
            message_data = event.data
            username = message_data.get("username", "")
            message = message_data.get("message", "")
            channel = message_data.get("channel", "")
            
            # Check if message is directed at the bot
            if self._should_respond_to_message(message):
                response = await self.generate_response(
                    message=message,
                    username=username,
                    channel=channel,
                    context=message_data
                )
                
                if response:
                    # Emit AI response event
                    await self.event_bus.emit(Event(
                        type=EventType.AI_RESPONSE,
                        data={
                            "response": response,
                            "original_message": message,
                            "username": username,
                            "channel": channel,
                            "timestamp": datetime.now().isoformat()
                        },
                        source="ai_engine",
                        timestamp=datetime.now()
                    ))
                    
        except Exception as e:
            logger.error(f"Error handling chat message: {e}")
    
    async def _handle_ai_request(self, event: Event) -> None:
        """Handle direct AI requests."""
        if not self._running:
            return
        
        try:
            request_data = event.data
            prompt = request_data.get("prompt", "")
            context = request_data.get("context", {})
            
            response = await self.generate_response(
                message=prompt,
                context=context
            )
            
            if response:
                # Emit AI response event
                await self.event_bus.emit(Event(
                    type=EventType.AI_RESPONSE,
                    data={
                        "response": response,
                        "original_prompt": prompt,
                        "context": context,
                        "timestamp": datetime.now().isoformat()
                    },
                    source="ai_engine",
                    timestamp=datetime.now()
                ))
                
        except Exception as e:
            logger.error(f"Error handling AI request: {e}")
    
    def _should_respond_to_message(self, message: str) -> bool:
        """
        Determine if the bot should respond to a message.
        
        Args:
            message: The chat message
            
        Returns:
            True if the bot should respond
        """
        # Simple logic - respond to messages that mention the bot or contain keywords
        message_lower = message.lower()
        
        # Check for bot mentions
        bot_mentions = ["harmony", "bot", "ai", "@harmony"]
        if any(mention in message_lower for mention in bot_mentions):
            return True
        
        # Check for question patterns
        question_patterns = ["?", "what", "how", "why", "when", "where", "who"]
        if any(pattern in message_lower for pattern in question_patterns):
            return True
        
        return False
    
    async def generate_response(
        self,
        message: str,
        username: Optional[str] = None,
        channel: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Optional[str]:
        """
        Generate an AI response to a message.
        
        Args:
            message: The input message
            username: Username of the sender
            channel: Channel where message was sent
            context: Additional context
            
        Returns:
            Generated response or None if failed
        """
        if not self.client or not self._running:
            return None
        
        try:
            # Build the prompt with personality and context
            prompt = self._build_prompt(message, username, channel, context)
            
            # Generate response using Gemini
            response = await self.client.aio.models.generate_content(
                model=self.config.gemini_model,
                contents=prompt,
                config=GenerateContentConfig(
                    max_output_tokens=200,
                    temperature=0.7,
                    top_p=0.9
                )
            )
            
            if response.text:
                # Add to conversation history
                self._add_to_history(message, response.text, username)
                return response.text.strip()
            else:
                logger.warning("Empty response from Gemini API")
                return None
                
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return None
    
    def _build_prompt(
        self,
        message: str,
        username: Optional[str] = None,
        channel: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Build a prompt with personality and context."""
        # Get personality context
        personality_prompt = self.personality_manager.get_system_prompt()
        
        # Build the full prompt
        prompt_parts = [
            personality_prompt,
            "\nYou are Harmony, an AI co-host for a Twitch stream.",
            "Respond naturally and engagingly to chat messages.",
            "Keep responses concise (1-2 sentences) and appropriate for live streaming.",
        ]
        
        # Add conversation history context
        if self._conversation_history:
            prompt_parts.append("\nRecent conversation:")
            for entry in self._conversation_history[-5:]:  # Last 5 exchanges
                prompt_parts.append(f"User: {entry['message']}")
                prompt_parts.append(f"Harmony: {entry['response']}")
        
        # Add current message
        if username:
            prompt_parts.append(f"\n{username} says: {message}")
        else:
            prompt_parts.append(f"\nMessage: {message}")
        
        prompt_parts.append("\nHarmony responds:")
        
        return "\n".join(prompt_parts)
    
    def _add_to_history(self, message: str, response: str, username: Optional[str] = None) -> None:
        """Add exchange to conversation history."""
        self._conversation_history.append({
            "message": message,
            "response": response,
            "username": username,
            "timestamp": datetime.now().isoformat()
        })
        
        # Trim history if too long
        if len(self._conversation_history) > self._max_history_length:
            self._conversation_history = self._conversation_history[-self._max_history_length:]
    
    @property
    def is_running(self) -> bool:
        """Check if the AI Engine is running."""
        return self._running
    
    @property
    def model_info(self) -> Dict[str, Any]:
        """Get information about the current model."""
        return {
            "model": self.config.gemini_model,
            "provider": "Google Gemini",
            "initialized": self._initialized,
            "running": self._running
        }
