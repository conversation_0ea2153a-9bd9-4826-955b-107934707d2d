#!/usr/bin/env python3
"""
Main entry point for Harmony AI Twitch Co-Host <PERSON><PERSON>.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

import click
from dotenv import load_dotenv

from harmony.core.bot import HarmonyBot
from harmony.utils.logger import get_logger

# Load environment variables
load_dotenv()

logger = get_logger(__name__)


@click.command()
@click.option(
    "--config", 
    "-c", 
    default=None, 
    help="Path to configuration file"
)
@click.option(
    "--debug", 
    "-d", 
    is_flag=True, 
    help="Enable debug mode"
)
@click.option(
    "--features", 
    "-f", 
    default=None, 
    help="Comma-separated list of features to enable (e.g., chat,tts,trivia)"
)
@click.option(
    "--gui", 
    "-g", 
    is_flag=True, 
    help="Launch with GUI interface"
)
@click.option(
    "--mock", 
    "-m", 
    is_flag=True, 
    help="Use mock APIs for testing"
)
def main(config, debug, features, gui, mock):
    """
    Launch Harmony AI Twitch Co-Host Bot.
    
    Examples:
        python run.py                           # Run with default settings
        python run.py --debug                   # Run in debug mode
        python run.py --config config/prod.yaml # Use specific config
        python run.py --features chat,tts       # Enable only specific features
        python run.py --gui                     # Launch with GUI
    """
    
    # Set environment variables based on CLI options
    if debug:
        os.environ["DEBUG"] = "true"
        os.environ["LOG_LEVEL"] = "DEBUG"
    
    if mock:
        os.environ["MOCK_APIS"] = "true"
    
    if features:
        # Parse feature list and set environment variables
        feature_list = [f.strip() for f in features.split(",")]
        
        # Disable all features first
        os.environ["ENABLE_TTS"] = "false"
        os.environ["ENABLE_STT"] = "false"
        os.environ["ENABLE_TRIVIA"] = "false"
        os.environ["ENABLE_MODERATION"] = "false"
        os.environ["ENABLE_GOAL_TRACKING"] = "false"
        os.environ["ENABLE_REMINDERS"] = "false"
        
        # Enable specified features
        feature_map = {
            "chat": None,  # Chat is always enabled
            "tts": "ENABLE_TTS",
            "stt": "ENABLE_STT", 
            "trivia": "ENABLE_TRIVIA",
            "moderation": "ENABLE_MODERATION",
            "goals": "ENABLE_GOAL_TRACKING",
            "reminders": "ENABLE_REMINDERS",
        }
        
        for feature in feature_list:
            if feature in feature_map and feature_map[feature]:
                os.environ[feature_map[feature]] = "true"
    
    if gui:
        # Launch GUI version
        try:
            from harmony.gui.main import launch_gui
            launch_gui(config)
        except ImportError as e:
            logger.error(f"GUI dependencies not available: {e}")
            logger.info("Install GUI dependencies with: pip install 'harmony-bot[gui]'")
            sys.exit(1)
    else:
        # Launch headless version
        asyncio.run(run_bot(config))


async def run_bot(config_path=None):
    """
    Run the bot in headless mode.
    
    Args:
        config_path: Optional path to configuration file
    """
    try:
        logger.info("Starting Harmony AI Twitch Co-Host Bot...")
        
        # Create and run bot
        bot = HarmonyBot(config_path)
        await bot.run()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


def check_dependencies():
    """Check if required dependencies are available."""
    missing_deps = []
    
    try:
        import twitchio
    except ImportError:
        missing_deps.append("twitchio")
    
    try:
        import google.genai
    except ImportError:
        missing_deps.append("google-genai")
    
    try:
        import elevenlabs
    except ImportError:
        missing_deps.append("elevenlabs")
    
    if missing_deps:
        logger.error(f"Missing required dependencies: {', '.join(missing_deps)}")
        logger.info("Install dependencies with: pip install -r requirements.txt")
        sys.exit(1)


def check_environment():
    """Check if required environment variables are set."""
    required_vars = [
        "GOOGLE_API_KEY",
        "ELEVENLABS_API_KEY", 
        "TWITCH_CLIENT_ID",
        "TWITCH_CLIENT_SECRET",
        "SECRET_KEY",
        "TOKEN_ENCRYPTION_PASSWORD"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.info("Copy .env.example to .env and fill in your API keys")
        sys.exit(1)


def create_directories():
    """Create necessary directories if they don't exist."""
    directories = [
        "data",
        "data/logs", 
        "data/audio",
        "config",
        "cache"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)


if __name__ == "__main__":
    # Perform startup checks
    check_dependencies()
    check_environment()
    create_directories()
    
    # Run the application
    main()
